# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# compile C with C:/ST/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc.exe
C_DEFINES = -DARM_MATH_CM4 -DSTM32F407xx -DUSE_HAL_DRIVER -D__FPU_PRESENT=1U

C_INCLUDES = -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS\Include -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Board_STM32F407\Config -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\ThirdParty\USB_HOST\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceDrivers\USB\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Inc\Legacy -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS\Device\ST\STM32F4xx\Include

C_FLAGS = -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11

