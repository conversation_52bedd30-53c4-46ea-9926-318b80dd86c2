
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "ASM"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_ASM
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s" "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_ASM
  "ARM_MATH_CM4"
  "DEBUG"
  "STM32F407xx"
  "USE_HAL_DRIVER"
  "__FPU_PRESENT=1U"
  )

# The include file search paths:
set(CMAKE_ASM_TARGET_INCLUDE_PATH
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Board_STM32F407/Config"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Board_STM32F407/Startup"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/I2C/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/SPI/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/UART/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/USB/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_I2C/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_SPI/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_USB/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_UART/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMSIS/Include"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/ThirdParty/FreeRTOS/include"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/ThirdParty/FATFS/src"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/ThirdParty/USB_HOST/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Inc"
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/app.c" "CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj" "gcc" "CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj.d"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/i2c_impl.c" "CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj" "gcc" "CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj.d"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/main.c" "CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj" "gcc" "CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj.d"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/uart_impl.c" "CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj" "gcc" "CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj.d"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/usb_impl.c" "CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj" "gcc" "CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
