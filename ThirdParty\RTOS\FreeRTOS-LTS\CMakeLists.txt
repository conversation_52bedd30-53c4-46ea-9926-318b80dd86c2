# CMakeLists.txt for FreeRTOS-LTS

# Check if FreeRTOS source files exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/Source/tasks.c)
    # FreeRTOS Core source files
    set(FREERTOS_SOURCES
        ${CMAKE_CURRENT_SOURCE_DIR}/Source/tasks.c
        ${CMAKE_CURRENT_SOURCE_DIR}/Source/queue.c
        ${CMAKE_CURRENT_SOURCE_DIR}/Source/list.c
        ${CMAKE_CURRENT_SOURCE_DIR}/Source/timers.c
        ${CMAKE_CURRENT_SOURCE_DIR}/Source/event_groups.c
        ${CMAKE_CURRENT_SOURCE_DIR}/Source/stream_buffer.c
        ${CMAKE_CURRENT_SOURCE_DIR}/Source/croutine.c
    )

    # FreeRTOS portable layer for ARM Cortex-M4
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/Source/portable/GCC/ARM_CM4F/port.c)
        list(APPEND FREERTOS_SOURCES
            ${CMAKE_CURRENT_SOURCE_DIR}/Source/portable/GCC/ARM_CM4F/port.c
        )
    endif()

    # Memory management (heap_4.c is commonly used)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/Source/portable/MemMang/heap_4.c)
        list(APPEND FREERTOS_SOURCES
            ${CMAKE_CURRENT_SOURCE_DIR}/Source/portable/MemMang/heap_4.c
        )
    endif()

    # Create FreeRTOS library
    add_library(freertos STATIC ${FREERTOS_SOURCES})

    # Set target properties
    set_target_properties(freertos PROPERTIES LINKER_LANGUAGE C)

    # Include directories
    target_include_directories(freertos PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/Source/include
        ${CMAKE_CURRENT_SOURCE_DIR}/Source/portable/GCC/ARM_CM4F
        ${CMAKE_SOURCE_DIR}/Board_STM32F407/Config  # For FreeRTOSConfig.h
    )

    # Compiler definitions
    target_compile_definitions(freertos PUBLIC
        STM32F407xx
        ARM_MATH_CM4
        __FPU_PRESENT=1U
    )

    # Link with CMSIS and HAL
    target_link_libraries(freertos PUBLIC
        cmsis
        stm32f4xx_hal
    )

else()
    # Create a dummy FreeRTOS library if source files don't exist
    add_library(freertos INTERFACE)
    target_include_directories(freertos INTERFACE
        ${CMAKE_CURRENT_SOURCE_DIR}
    )
    message(WARNING "FreeRTOS source files not found. Creating dummy library.")
endif()
