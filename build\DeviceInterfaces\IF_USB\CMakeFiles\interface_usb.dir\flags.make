# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# compile C with C:/ST/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc.exe
C_DEFINES = 

C_INCLUDES = -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceInterfaces\IF_USB\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceDrivers\USB\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\ThirdParty\USB_HOST\Inc

C_FLAGS = -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11

