# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe

# The command to remove a file.
RM = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/STM32F4_DevKit.dir/all
all: Board_STM32F407/all
all: Middleware/all
all: DeviceDrivers/all
all: DeviceInterfaces/all
all: ThirdParty/all
all: Application/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: Board_STM32F407/preinstall
preinstall: Middleware/preinstall
preinstall: DeviceDrivers/preinstall
preinstall: DeviceInterfaces/preinstall
preinstall: ThirdParty/preinstall
preinstall: Application/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/STM32F4_DevKit.dir/clean
clean: CMakeFiles/flash.dir/clean
clean: CMakeFiles/debug.dir/clean
clean: Board_STM32F407/clean
clean: Middleware/clean
clean: DeviceDrivers/clean
clean: DeviceInterfaces/clean
clean: ThirdParty/clean
clean: Application/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory Application

# Recursive "all" directory target.
Application/all: Application/CMakeFiles/app_src.dir/all
.PHONY : Application/all

# Recursive "preinstall" directory target.
Application/preinstall:
.PHONY : Application/preinstall

# Recursive "clean" directory target.
Application/clean: Application/CMakeFiles/app_src.dir/clean
.PHONY : Application/clean

#=============================================================================
# Directory level rules for directory Board_STM32F407

# Recursive "all" directory target.
Board_STM32F407/all: Board_STM32F407/CMakeFiles/board_stm32f407.dir/all
.PHONY : Board_STM32F407/all

# Recursive "preinstall" directory target.
Board_STM32F407/preinstall:
.PHONY : Board_STM32F407/preinstall

# Recursive "clean" directory target.
Board_STM32F407/clean: Board_STM32F407/CMakeFiles/board_stm32f407.dir/clean
.PHONY : Board_STM32F407/clean

#=============================================================================
# Directory level rules for directory DeviceDrivers

# Recursive "all" directory target.
DeviceDrivers/all: DeviceDrivers/I2C/all
DeviceDrivers/all: DeviceDrivers/SPI/all
DeviceDrivers/all: DeviceDrivers/USB/all
DeviceDrivers/all: DeviceDrivers/UART/all
.PHONY : DeviceDrivers/all

# Recursive "preinstall" directory target.
DeviceDrivers/preinstall: DeviceDrivers/I2C/preinstall
DeviceDrivers/preinstall: DeviceDrivers/SPI/preinstall
DeviceDrivers/preinstall: DeviceDrivers/USB/preinstall
DeviceDrivers/preinstall: DeviceDrivers/UART/preinstall
.PHONY : DeviceDrivers/preinstall

# Recursive "clean" directory target.
DeviceDrivers/clean: DeviceDrivers/I2C/clean
DeviceDrivers/clean: DeviceDrivers/SPI/clean
DeviceDrivers/clean: DeviceDrivers/USB/clean
DeviceDrivers/clean: DeviceDrivers/UART/clean
.PHONY : DeviceDrivers/clean

#=============================================================================
# Directory level rules for directory DeviceDrivers/I2C

# Recursive "all" directory target.
DeviceDrivers/I2C/all: DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/all
.PHONY : DeviceDrivers/I2C/all

# Recursive "preinstall" directory target.
DeviceDrivers/I2C/preinstall:
.PHONY : DeviceDrivers/I2C/preinstall

# Recursive "clean" directory target.
DeviceDrivers/I2C/clean: DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/clean
.PHONY : DeviceDrivers/I2C/clean

#=============================================================================
# Directory level rules for directory DeviceDrivers/SPI

# Recursive "all" directory target.
DeviceDrivers/SPI/all: DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/all
.PHONY : DeviceDrivers/SPI/all

# Recursive "preinstall" directory target.
DeviceDrivers/SPI/preinstall:
.PHONY : DeviceDrivers/SPI/preinstall

# Recursive "clean" directory target.
DeviceDrivers/SPI/clean: DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/clean
.PHONY : DeviceDrivers/SPI/clean

#=============================================================================
# Directory level rules for directory DeviceDrivers/UART

# Recursive "all" directory target.
DeviceDrivers/UART/all: DeviceDrivers/UART/CMakeFiles/driver_uart.dir/all
.PHONY : DeviceDrivers/UART/all

# Recursive "preinstall" directory target.
DeviceDrivers/UART/preinstall:
.PHONY : DeviceDrivers/UART/preinstall

# Recursive "clean" directory target.
DeviceDrivers/UART/clean: DeviceDrivers/UART/CMakeFiles/driver_uart.dir/clean
.PHONY : DeviceDrivers/UART/clean

#=============================================================================
# Directory level rules for directory DeviceDrivers/USB

# Recursive "all" directory target.
DeviceDrivers/USB/all: DeviceDrivers/USB/CMakeFiles/driver_usb.dir/all
.PHONY : DeviceDrivers/USB/all

# Recursive "preinstall" directory target.
DeviceDrivers/USB/preinstall:
.PHONY : DeviceDrivers/USB/preinstall

# Recursive "clean" directory target.
DeviceDrivers/USB/clean: DeviceDrivers/USB/CMakeFiles/driver_usb.dir/clean
.PHONY : DeviceDrivers/USB/clean

#=============================================================================
# Directory level rules for directory DeviceInterfaces

# Recursive "all" directory target.
DeviceInterfaces/all: DeviceInterfaces/IF_I2C/all
DeviceInterfaces/all: DeviceInterfaces/IF_SPI/all
DeviceInterfaces/all: DeviceInterfaces/IF_USB/all
DeviceInterfaces/all: DeviceInterfaces/IF_UART/all
.PHONY : DeviceInterfaces/all

# Recursive "preinstall" directory target.
DeviceInterfaces/preinstall: DeviceInterfaces/IF_I2C/preinstall
DeviceInterfaces/preinstall: DeviceInterfaces/IF_SPI/preinstall
DeviceInterfaces/preinstall: DeviceInterfaces/IF_USB/preinstall
DeviceInterfaces/preinstall: DeviceInterfaces/IF_UART/preinstall
.PHONY : DeviceInterfaces/preinstall

# Recursive "clean" directory target.
DeviceInterfaces/clean: DeviceInterfaces/IF_I2C/clean
DeviceInterfaces/clean: DeviceInterfaces/IF_SPI/clean
DeviceInterfaces/clean: DeviceInterfaces/IF_USB/clean
DeviceInterfaces/clean: DeviceInterfaces/IF_UART/clean
.PHONY : DeviceInterfaces/clean

#=============================================================================
# Directory level rules for directory DeviceInterfaces/IF_I2C

# Recursive "all" directory target.
DeviceInterfaces/IF_I2C/all: DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/all
.PHONY : DeviceInterfaces/IF_I2C/all

# Recursive "preinstall" directory target.
DeviceInterfaces/IF_I2C/preinstall:
.PHONY : DeviceInterfaces/IF_I2C/preinstall

# Recursive "clean" directory target.
DeviceInterfaces/IF_I2C/clean: DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/clean
.PHONY : DeviceInterfaces/IF_I2C/clean

#=============================================================================
# Directory level rules for directory DeviceInterfaces/IF_SPI

# Recursive "all" directory target.
DeviceInterfaces/IF_SPI/all: DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/all
.PHONY : DeviceInterfaces/IF_SPI/all

# Recursive "preinstall" directory target.
DeviceInterfaces/IF_SPI/preinstall:
.PHONY : DeviceInterfaces/IF_SPI/preinstall

# Recursive "clean" directory target.
DeviceInterfaces/IF_SPI/clean: DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/clean
.PHONY : DeviceInterfaces/IF_SPI/clean

#=============================================================================
# Directory level rules for directory DeviceInterfaces/IF_UART

# Recursive "all" directory target.
DeviceInterfaces/IF_UART/all: DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/all
.PHONY : DeviceInterfaces/IF_UART/all

# Recursive "preinstall" directory target.
DeviceInterfaces/IF_UART/preinstall:
.PHONY : DeviceInterfaces/IF_UART/preinstall

# Recursive "clean" directory target.
DeviceInterfaces/IF_UART/clean: DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/clean
.PHONY : DeviceInterfaces/IF_UART/clean

#=============================================================================
# Directory level rules for directory DeviceInterfaces/IF_USB

# Recursive "all" directory target.
DeviceInterfaces/IF_USB/all: DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/all
.PHONY : DeviceInterfaces/IF_USB/all

# Recursive "preinstall" directory target.
DeviceInterfaces/IF_USB/preinstall:
.PHONY : DeviceInterfaces/IF_USB/preinstall

# Recursive "clean" directory target.
DeviceInterfaces/IF_USB/clean: DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/clean
.PHONY : DeviceInterfaces/IF_USB/clean

#=============================================================================
# Directory level rules for directory Middleware

# Recursive "all" directory target.
Middleware/all: Middleware/STM32F4xx_HAL_Driver/all
Middleware/all: Middleware/CMSIS/all
.PHONY : Middleware/all

# Recursive "preinstall" directory target.
Middleware/preinstall: Middleware/STM32F4xx_HAL_Driver/preinstall
Middleware/preinstall: Middleware/CMSIS/preinstall
.PHONY : Middleware/preinstall

# Recursive "clean" directory target.
Middleware/clean: Middleware/STM32F4xx_HAL_Driver/clean
Middleware/clean: Middleware/CMSIS/clean
.PHONY : Middleware/clean

#=============================================================================
# Directory level rules for directory Middleware/CMSIS

# Recursive "all" directory target.
Middleware/CMSIS/all: Middleware/CMSIS/CMakeFiles/cmsis.dir/all
.PHONY : Middleware/CMSIS/all

# Recursive "preinstall" directory target.
Middleware/CMSIS/preinstall:
.PHONY : Middleware/CMSIS/preinstall

# Recursive "clean" directory target.
Middleware/CMSIS/clean: Middleware/CMSIS/CMakeFiles/cmsis.dir/clean
.PHONY : Middleware/CMSIS/clean

#=============================================================================
# Directory level rules for directory Middleware/STM32F4xx_HAL_Driver

# Recursive "all" directory target.
Middleware/STM32F4xx_HAL_Driver/all: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all
.PHONY : Middleware/STM32F4xx_HAL_Driver/all

# Recursive "preinstall" directory target.
Middleware/STM32F4xx_HAL_Driver/preinstall:
.PHONY : Middleware/STM32F4xx_HAL_Driver/preinstall

# Recursive "clean" directory target.
Middleware/STM32F4xx_HAL_Driver/clean: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/clean
.PHONY : Middleware/STM32F4xx_HAL_Driver/clean

#=============================================================================
# Directory level rules for directory ThirdParty

# Recursive "all" directory target.
ThirdParty/all: ThirdParty/FreeRTOS/all
ThirdParty/all: ThirdParty/FATFS/all
ThirdParty/all: ThirdParty/USB_HOST/all
ThirdParty/all: ThirdParty/RTOS/all
.PHONY : ThirdParty/all

# Recursive "preinstall" directory target.
ThirdParty/preinstall: ThirdParty/FreeRTOS/preinstall
ThirdParty/preinstall: ThirdParty/FATFS/preinstall
ThirdParty/preinstall: ThirdParty/USB_HOST/preinstall
ThirdParty/preinstall: ThirdParty/RTOS/preinstall
.PHONY : ThirdParty/preinstall

# Recursive "clean" directory target.
ThirdParty/clean: ThirdParty/FreeRTOS/clean
ThirdParty/clean: ThirdParty/FATFS/clean
ThirdParty/clean: ThirdParty/USB_HOST/clean
ThirdParty/clean: ThirdParty/RTOS/clean
.PHONY : ThirdParty/clean

#=============================================================================
# Directory level rules for directory ThirdParty/FATFS

# Recursive "all" directory target.
ThirdParty/FATFS/all:
.PHONY : ThirdParty/FATFS/all

# Recursive "preinstall" directory target.
ThirdParty/FATFS/preinstall:
.PHONY : ThirdParty/FATFS/preinstall

# Recursive "clean" directory target.
ThirdParty/FATFS/clean:
.PHONY : ThirdParty/FATFS/clean

#=============================================================================
# Directory level rules for directory ThirdParty/FreeRTOS

# Recursive "all" directory target.
ThirdParty/FreeRTOS/all:
.PHONY : ThirdParty/FreeRTOS/all

# Recursive "preinstall" directory target.
ThirdParty/FreeRTOS/preinstall:
.PHONY : ThirdParty/FreeRTOS/preinstall

# Recursive "clean" directory target.
ThirdParty/FreeRTOS/clean:
.PHONY : ThirdParty/FreeRTOS/clean

#=============================================================================
# Directory level rules for directory ThirdParty/RTOS

# Recursive "all" directory target.
ThirdParty/RTOS/all: ThirdParty/RTOS/FreeRTOS-LTS/all
.PHONY : ThirdParty/RTOS/all

# Recursive "preinstall" directory target.
ThirdParty/RTOS/preinstall: ThirdParty/RTOS/FreeRTOS-LTS/preinstall
.PHONY : ThirdParty/RTOS/preinstall

# Recursive "clean" directory target.
ThirdParty/RTOS/clean: ThirdParty/RTOS/FreeRTOS-LTS/clean
.PHONY : ThirdParty/RTOS/clean

#=============================================================================
# Directory level rules for directory ThirdParty/RTOS/FreeRTOS-LTS

# Recursive "all" directory target.
ThirdParty/RTOS/FreeRTOS-LTS/all:
.PHONY : ThirdParty/RTOS/FreeRTOS-LTS/all

# Recursive "preinstall" directory target.
ThirdParty/RTOS/FreeRTOS-LTS/preinstall:
.PHONY : ThirdParty/RTOS/FreeRTOS-LTS/preinstall

# Recursive "clean" directory target.
ThirdParty/RTOS/FreeRTOS-LTS/clean:
.PHONY : ThirdParty/RTOS/FreeRTOS-LTS/clean

#=============================================================================
# Directory level rules for directory ThirdParty/USB_HOST

# Recursive "all" directory target.
ThirdParty/USB_HOST/all:
.PHONY : ThirdParty/USB_HOST/all

# Recursive "preinstall" directory target.
ThirdParty/USB_HOST/preinstall:
.PHONY : ThirdParty/USB_HOST/preinstall

# Recursive "clean" directory target.
ThirdParty/USB_HOST/clean:
.PHONY : ThirdParty/USB_HOST/clean

#=============================================================================
# Target rules for target CMakeFiles/STM32F4_DevKit.dir

# All Build rule for target.
CMakeFiles/STM32F4_DevKit.dir/all: Board_STM32F407/CMakeFiles/board_stm32f407.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: Middleware/CMSIS/CMakeFiles/cmsis.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: DeviceDrivers/USB/CMakeFiles/driver_usb.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: DeviceDrivers/UART/CMakeFiles/driver_uart.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/all
CMakeFiles/STM32F4_DevKit.dir/all: Application/CMakeFiles/app_src.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=1,2,3,4,5,6,7 "Built target STM32F4_DevKit"
.PHONY : CMakeFiles/STM32F4_DevKit.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/STM32F4_DevKit.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 50
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/STM32F4_DevKit.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : CMakeFiles/STM32F4_DevKit.dir/rule

# Convenience name for target.
STM32F4_DevKit: CMakeFiles/STM32F4_DevKit.dir/rule
.PHONY : STM32F4_DevKit

# clean rule for target.
CMakeFiles/STM32F4_DevKit.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/clean
.PHONY : CMakeFiles/STM32F4_DevKit.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/flash.dir

# All Build rule for target.
CMakeFiles/flash.dir/all: CMakeFiles/STM32F4_DevKit.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\flash.dir\build.make CMakeFiles/flash.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\flash.dir\build.make CMakeFiles/flash.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=27 "Built target flash"
.PHONY : CMakeFiles/flash.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/flash.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/flash.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : CMakeFiles/flash.dir/rule

# Convenience name for target.
flash: CMakeFiles/flash.dir/rule
.PHONY : flash

# clean rule for target.
CMakeFiles/flash.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\flash.dir\build.make CMakeFiles/flash.dir/clean
.PHONY : CMakeFiles/flash.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/debug.dir

# All Build rule for target.
CMakeFiles/debug.dir/all: CMakeFiles/STM32F4_DevKit.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug.dir\build.make CMakeFiles/debug.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug.dir\build.make CMakeFiles/debug.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=18 "Built target debug"
.PHONY : CMakeFiles/debug.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/debug.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/debug.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : CMakeFiles/debug.dir/rule

# Convenience name for target.
debug: CMakeFiles/debug.dir/rule
.PHONY : debug

# clean rule for target.
CMakeFiles/debug.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug.dir\build.make CMakeFiles/debug.dir/clean
.PHONY : CMakeFiles/debug.dir/clean

#=============================================================================
# Target rules for target Board_STM32F407/CMakeFiles/board_stm32f407.dir

# All Build rule for target.
Board_STM32F407/CMakeFiles/board_stm32f407.dir/all: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all
Board_STM32F407/CMakeFiles/board_stm32f407.dir/all: Middleware/CMSIS/CMakeFiles/cmsis.dir/all
	$(MAKE) $(MAKESILENT) -f Board_STM32F407\CMakeFiles\board_stm32f407.dir\build.make Board_STM32F407/CMakeFiles/board_stm32f407.dir/depend
	$(MAKE) $(MAKESILENT) -f Board_STM32F407\CMakeFiles\board_stm32f407.dir\build.make Board_STM32F407/CMakeFiles/board_stm32f407.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=13,14,15 "Built target board_stm32f407"
.PHONY : Board_STM32F407/CMakeFiles/board_stm32f407.dir/all

# Build rule for subdir invocation for target.
Board_STM32F407/CMakeFiles/board_stm32f407.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Board_STM32F407/CMakeFiles/board_stm32f407.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : Board_STM32F407/CMakeFiles/board_stm32f407.dir/rule

# Convenience name for target.
board_stm32f407: Board_STM32F407/CMakeFiles/board_stm32f407.dir/rule
.PHONY : board_stm32f407

# clean rule for target.
Board_STM32F407/CMakeFiles/board_stm32f407.dir/clean:
	$(MAKE) $(MAKESILENT) -f Board_STM32F407\CMakeFiles\board_stm32f407.dir\build.make Board_STM32F407/CMakeFiles/board_stm32f407.dir/clean
.PHONY : Board_STM32F407/CMakeFiles/board_stm32f407.dir/clean

#=============================================================================
# Target rules for target Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir

# All Build rule for target.
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all: Middleware/CMSIS/CMakeFiles/cmsis.dir/all
	$(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/depend
	$(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52 "Built target stm32f4xx_hal"
.PHONY : Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all

# Build rule for subdir invocation for target.
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 19
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/rule

# Convenience name for target.
stm32f4xx_hal: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/rule
.PHONY : stm32f4xx_hal

# clean rule for target.
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/clean:
	$(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/clean
.PHONY : Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/clean

#=============================================================================
# Target rules for target Middleware/CMSIS/CMakeFiles/cmsis.dir

# All Build rule for target.
Middleware/CMSIS/CMakeFiles/cmsis.dir/all:
	$(MAKE) $(MAKESILENT) -f Middleware\CMSIS\CMakeFiles\cmsis.dir\build.make Middleware/CMSIS/CMakeFiles/cmsis.dir/depend
	$(MAKE) $(MAKESILENT) -f Middleware\CMSIS\CMakeFiles\cmsis.dir\build.make Middleware/CMSIS/CMakeFiles/cmsis.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=16,17 "Built target cmsis"
.PHONY : Middleware/CMSIS/CMakeFiles/cmsis.dir/all

# Build rule for subdir invocation for target.
Middleware/CMSIS/CMakeFiles/cmsis.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Middleware/CMSIS/CMakeFiles/cmsis.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : Middleware/CMSIS/CMakeFiles/cmsis.dir/rule

# Convenience name for target.
cmsis: Middleware/CMSIS/CMakeFiles/cmsis.dir/rule
.PHONY : cmsis

# clean rule for target.
Middleware/CMSIS/CMakeFiles/cmsis.dir/clean:
	$(MAKE) $(MAKESILENT) -f Middleware\CMSIS\CMakeFiles\cmsis.dir\build.make Middleware/CMSIS/CMakeFiles/cmsis.dir/clean
.PHONY : Middleware/CMSIS/CMakeFiles/cmsis.dir/clean

#=============================================================================
# Target rules for target DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir

# All Build rule for target.
DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/all:
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\I2C\CMakeFiles\driver_i2c.dir\build.make DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/depend
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\I2C\CMakeFiles\driver_i2c.dir\build.make DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=19,20 "Built target driver_i2c"
.PHONY : DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/all

# Build rule for subdir invocation for target.
DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/rule

# Convenience name for target.
driver_i2c: DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/rule
.PHONY : driver_i2c

# clean rule for target.
DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/clean:
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\I2C\CMakeFiles\driver_i2c.dir\build.make DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/clean
.PHONY : DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/clean

#=============================================================================
# Target rules for target DeviceDrivers/SPI/CMakeFiles/driver_spi.dir

# All Build rule for target.
DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/all: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all
DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/all: Middleware/CMSIS/CMakeFiles/cmsis.dir/all
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\SPI\CMakeFiles\driver_spi.dir\build.make DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/depend
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\SPI\CMakeFiles\driver_spi.dir\build.make DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=21,22 "Built target driver_spi"
.PHONY : DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/all

# Build rule for subdir invocation for target.
DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 21
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/rule

# Convenience name for target.
driver_spi: DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/rule
.PHONY : driver_spi

# clean rule for target.
DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/clean:
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\SPI\CMakeFiles\driver_spi.dir\build.make DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/clean
.PHONY : DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/clean

#=============================================================================
# Target rules for target DeviceDrivers/USB/CMakeFiles/driver_usb.dir

# All Build rule for target.
DeviceDrivers/USB/CMakeFiles/driver_usb.dir/all: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all
DeviceDrivers/USB/CMakeFiles/driver_usb.dir/all: Middleware/CMSIS/CMakeFiles/cmsis.dir/all
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\USB\CMakeFiles\driver_usb.dir\build.make DeviceDrivers/USB/CMakeFiles/driver_usb.dir/depend
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\USB\CMakeFiles\driver_usb.dir\build.make DeviceDrivers/USB/CMakeFiles/driver_usb.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=25,26 "Built target driver_usb"
.PHONY : DeviceDrivers/USB/CMakeFiles/driver_usb.dir/all

# Build rule for subdir invocation for target.
DeviceDrivers/USB/CMakeFiles/driver_usb.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 21
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 DeviceDrivers/USB/CMakeFiles/driver_usb.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : DeviceDrivers/USB/CMakeFiles/driver_usb.dir/rule

# Convenience name for target.
driver_usb: DeviceDrivers/USB/CMakeFiles/driver_usb.dir/rule
.PHONY : driver_usb

# clean rule for target.
DeviceDrivers/USB/CMakeFiles/driver_usb.dir/clean:
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\USB\CMakeFiles\driver_usb.dir\build.make DeviceDrivers/USB/CMakeFiles/driver_usb.dir/clean
.PHONY : DeviceDrivers/USB/CMakeFiles/driver_usb.dir/clean

#=============================================================================
# Target rules for target DeviceDrivers/UART/CMakeFiles/driver_uart.dir

# All Build rule for target.
DeviceDrivers/UART/CMakeFiles/driver_uart.dir/all: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all
DeviceDrivers/UART/CMakeFiles/driver_uart.dir/all: Middleware/CMSIS/CMakeFiles/cmsis.dir/all
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\UART\CMakeFiles\driver_uart.dir\build.make DeviceDrivers/UART/CMakeFiles/driver_uart.dir/depend
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\UART\CMakeFiles\driver_uart.dir\build.make DeviceDrivers/UART/CMakeFiles/driver_uart.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=23,24 "Built target driver_uart"
.PHONY : DeviceDrivers/UART/CMakeFiles/driver_uart.dir/all

# Build rule for subdir invocation for target.
DeviceDrivers/UART/CMakeFiles/driver_uart.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 21
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 DeviceDrivers/UART/CMakeFiles/driver_uart.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : DeviceDrivers/UART/CMakeFiles/driver_uart.dir/rule

# Convenience name for target.
driver_uart: DeviceDrivers/UART/CMakeFiles/driver_uart.dir/rule
.PHONY : driver_uart

# clean rule for target.
DeviceDrivers/UART/CMakeFiles/driver_uart.dir/clean:
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\UART\CMakeFiles\driver_uart.dir\build.make DeviceDrivers/UART/CMakeFiles/driver_uart.dir/clean
.PHONY : DeviceDrivers/UART/CMakeFiles/driver_uart.dir/clean

#=============================================================================
# Target rules for target DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir

# All Build rule for target.
DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/all: DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/all
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_I2C\CMakeFiles\interface_i2c.dir\build.make DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/depend
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_I2C\CMakeFiles\interface_i2c.dir\build.make DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=28,29 "Built target interface_i2c"
.PHONY : DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/all

# Build rule for subdir invocation for target.
DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/rule

# Convenience name for target.
interface_i2c: DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/rule
.PHONY : interface_i2c

# clean rule for target.
DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/clean:
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_I2C\CMakeFiles\interface_i2c.dir\build.make DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/clean
.PHONY : DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/clean

#=============================================================================
# Target rules for target DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir

# All Build rule for target.
DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/all: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all
DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/all: Middleware/CMSIS/CMakeFiles/cmsis.dir/all
DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/all: DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/all
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_SPI\CMakeFiles\interface_spi.dir\build.make DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/depend
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_SPI\CMakeFiles\interface_spi.dir\build.make DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=30,31 "Built target interface_spi"
.PHONY : DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/all

# Build rule for subdir invocation for target.
DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/rule

# Convenience name for target.
interface_spi: DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/rule
.PHONY : interface_spi

# clean rule for target.
DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/clean:
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_SPI\CMakeFiles\interface_spi.dir\build.make DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/clean
.PHONY : DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/clean

#=============================================================================
# Target rules for target DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir

# All Build rule for target.
DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/all: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all
DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/all: Middleware/CMSIS/CMakeFiles/cmsis.dir/all
DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/all: DeviceDrivers/USB/CMakeFiles/driver_usb.dir/all
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_USB\CMakeFiles\interface_usb.dir\build.make DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/depend
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_USB\CMakeFiles\interface_usb.dir\build.make DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=34,35 "Built target interface_usb"
.PHONY : DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/all

# Build rule for subdir invocation for target.
DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/rule

# Convenience name for target.
interface_usb: DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/rule
.PHONY : interface_usb

# clean rule for target.
DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/clean:
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_USB\CMakeFiles\interface_usb.dir\build.make DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/clean
.PHONY : DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/clean

#=============================================================================
# Target rules for target DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir

# All Build rule for target.
DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/all: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/all
DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/all: Middleware/CMSIS/CMakeFiles/cmsis.dir/all
DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/all: DeviceDrivers/UART/CMakeFiles/driver_uart.dir/all
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_UART\CMakeFiles\interface_uart.dir\build.make DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/depend
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_UART\CMakeFiles\interface_uart.dir\build.make DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=32,33 "Built target interface_uart"
.PHONY : DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/all

# Build rule for subdir invocation for target.
DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/rule

# Convenience name for target.
interface_uart: DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/rule
.PHONY : interface_uart

# clean rule for target.
DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/clean:
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_UART\CMakeFiles\interface_uart.dir\build.make DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/clean
.PHONY : DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/clean

#=============================================================================
# Target rules for target Application/CMakeFiles/app_src.dir

# All Build rule for target.
Application/CMakeFiles/app_src.dir/all:
	$(MAKE) $(MAKESILENT) -f Application\CMakeFiles\app_src.dir\build.make Application/CMakeFiles/app_src.dir/depend
	$(MAKE) $(MAKESILENT) -f Application\CMakeFiles\app_src.dir\build.make Application/CMakeFiles/app_src.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=8,9,10,11,12 "Built target app_src"
.PHONY : Application/CMakeFiles/app_src.dir/all

# Build rule for subdir invocation for target.
Application/CMakeFiles/app_src.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Application/CMakeFiles/app_src.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : Application/CMakeFiles/app_src.dir/rule

# Convenience name for target.
app_src: Application/CMakeFiles/app_src.dir/rule
.PHONY : app_src

# clean rule for target.
Application/CMakeFiles/app_src.dir/clean:
	$(MAKE) $(MAKESILENT) -f Application\CMakeFiles\app_src.dir\build.make Application/CMakeFiles/app_src.dir/clean
.PHONY : Application/CMakeFiles/app_src.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

