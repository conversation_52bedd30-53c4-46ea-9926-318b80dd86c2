#!/bin/bash

# STM32F4 DevKit Cross-Platform Build Script
# Supports Windows (MSYS2/MinGW), Linux, and macOS
# Author: Auto-generated for STM32F4_DevKit project

set -e  # Exit on any error

# Project configuration
PROJECT_NAME="STM32F4_DevKit"
PROJECT_DIR="cd $pwd"
BUILD_DIR="${PROJECT_DIR}/build"
TOOLCHAIN_FILE="${PROJECT_DIR}/Scripts/gcc-arm-none-eabi.cmake"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Platform detection
detect_platform() {
    case "$(uname -s)" in
        CYGWIN*|MINGW*|MSYS*)
            PLATFORM="windows"
            ;;
        Linux*)
            PLATFORM="linux"
            ;;
        Darwin*)
            PLATFORM="macos"
            ;;
        *)
            log_error "Unsupported platform: $(uname -s)"
            exit 1
            ;;
    esac
    log_info "Detected platform: $PLATFORM"
}

# Find ARM GCC toolchain
find_arm_toolchain() {
    local arm_gcc_paths=()
    
    case $PLATFORM in
        "windows")
            arm_gcc_paths=(
                "/c/ST/STM32CubeCLT_*/GNU-tools-for-STM32/bin"
                "/c/Program Files (x86)/GNU Arm Embedded Toolchain/*/bin"
                "/c/Program Files/GNU Arm Embedded Toolchain/*/bin"
                "/mingw64/bin"
                "/usr/bin"
            )
            MAKE_CMD="mingw32-make"
            CMAKE_GENERATOR="MinGW Makefiles"
            ;;
        "linux")
            arm_gcc_paths=(
                "/usr/bin"
                "/usr/local/bin"
                "/opt/gcc-arm-none-eabi/bin"
                "$HOME/.local/bin"
            )
            MAKE_CMD="make"
            CMAKE_GENERATOR="Unix Makefiles"
            ;;
        "macos")
            arm_gcc_paths=(
                "/usr/local/bin"
                "/opt/homebrew/bin"
                "/Applications/ARM/bin"
                "$HOME/.local/bin"
            )
            MAKE_CMD="make"
            CMAKE_GENERATOR="Unix Makefiles"
            ;;
    esac

    # Search for arm-none-eabi-gcc
    for path in "${arm_gcc_paths[@]}"; do
        if [[ -f "${path}/arm-none-eabi-gcc" ]] || [[ -f "${path}/arm-none-eabi-gcc.exe" ]]; then
            ARM_TOOLCHAIN_PATH="$path"
            log_success "Found ARM GCC toolchain at: $ARM_TOOLCHAIN_PATH"
            return 0
        fi
    done

    # Check if it's in PATH
    if command -v arm-none-eabi-gcc >/dev/null 2>&1; then
        ARM_TOOLCHAIN_PATH="$(dirname "$(which arm-none-eabi-gcc)")"
        log_success "Found ARM GCC toolchain in PATH: $ARM_TOOLCHAIN_PATH"
        return 0
    fi

    log_error "ARM GCC toolchain not found!"
    log_error "Please install one of the following:"
    log_error "  Windows: STM32CubeCLT or GNU Arm Embedded Toolchain"
    log_error "  Linux:   sudo apt-get install gcc-arm-none-eabi"
    log_error "  macOS:    brew install --cask gcc-arm-embedded"
    exit 1
}

# Check build tools
check_build_tools() {
    log_info "Checking build tools..."

    # Check CMake
    if ! command -v cmake >/dev/null 2>&1; then
        log_error "CMake not found! Please install CMake 3.16 or later."
        exit 1
    fi
    CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
    log_success "CMake found: $CMAKE_VERSION"

    # Check Make
    if ! command -v $MAKE_CMD >/dev/null 2>&1; then
        log_error "$MAKE_CMD not found!"
        case $PLATFORM in
            "windows")
                log_error "Please install MinGW-w64 or MSYS2"
                ;;
            "linux")
                log_error "Please install build-essential: sudo apt-get install build-essential"
                ;;
            "macos")
                log_error "Please install Xcode Command Line Tools: xcode-select --install"
                ;;
        esac
        exit 1
    fi
    log_success "Make found: $MAKE_CMD"

    # Check ARM toolchain
    find_arm_toolchain
}

# Clean build directory
clean_build() {
    log_info "Cleaning build directory..."
    if [[ -d "$BUILD_DIR" ]]; then
        rm -rf "$BUILD_DIR"
    fi
    mkdir -p "$BUILD_DIR"
    log_success "Build directory cleaned"
}

# Configure CMake
configure_cmake() {
    local build_type="${1:-Release}"
    log_info "Configuring CMake for $build_type build..."

    cd "$BUILD_DIR"
    
    cmake -G "$CMAKE_GENERATOR" \
          -DCMAKE_BUILD_TYPE="$build_type" \
          -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
          -DCMAKE_TOOLCHAIN_FILE="$TOOLCHAIN_FILE" \
          "$PROJECT_DIR"

    if [[ $? -eq 0 ]]; then
        log_success "CMake configuration completed"
    else
        log_error "CMake configuration failed"
        exit 1
    fi
}

# Build project
build_project() {
    local jobs="${1:-$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)}"
    log_info "Building project with $jobs parallel jobs..."

    cd "$BUILD_DIR"
    
    $MAKE_CMD -j"$jobs"

    if [[ $? -eq 0 ]]; then
        log_success "Build completed successfully!"
        show_build_artifacts
    else
        log_error "Build failed"
        exit 1
    fi
}

# Show build artifacts
show_build_artifacts() {
    log_info "Build artifacts:"
    cd "$BUILD_DIR"
    
    if [[ -f "${PROJECT_NAME}.elf" ]]; then
        echo "  ✓ ${PROJECT_NAME}.elf"
        arm-none-eabi-size "${PROJECT_NAME}.elf" 2>/dev/null || true
    fi
    
    [[ -f "${PROJECT_NAME}.bin" ]] && echo "  ✓ ${PROJECT_NAME}.bin"
    [[ -f "${PROJECT_NAME}.hex" ]] && echo "  ✓ ${PROJECT_NAME}.hex"
    [[ -f "${PROJECT_NAME}.map" ]] && echo "  ✓ ${PROJECT_NAME}.map"
    [[ -f "compile_commands.json" ]] && echo "  ✓ compile_commands.json"
}

# Flash firmware (if tools available)
flash_firmware() {
    log_info "Checking for flashing tools..."
    
    if command -v st-flash >/dev/null 2>&1; then
        log_info "Flashing with st-flash..."
        st-flash write "${BUILD_DIR}/${PROJECT_NAME}.bin" 0x8000000
    elif command -v openocd >/dev/null 2>&1; then
        log_info "OpenOCD found - you can flash manually"
        log_info "Command: openocd -f interface/stlink.cfg -f target/stm32f4x.cfg -c 'program ${BUILD_DIR}/${PROJECT_NAME}.elf verify reset exit'"
    else
        log_warning "No flashing tools found (st-flash, openocd)"
        log_info "You can flash manually using STM32CubeProgrammer or other tools"
    fi
}

# Show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build [debug|release]  - Build the project (default: release)"
    echo "  clean                  - Clean build directory"
    echo "  flash                  - Flash firmware to device"
    echo "  check                  - Check build environment"
    echo "  help                   - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 build              - Build release version"
    echo "  $0 build debug        - Build debug version"
    echo "  $0 clean build        - Clean and build"
    echo "  $0 flash              - Flash firmware"
}

# Main script logic
main() {
    log_info "STM32F4 DevKit Cross-Platform Build Script"
    log_info "=========================================="
    
    detect_platform
    
    case "${1:-build}" in
        "build")
            check_build_tools
            configure_cmake "${2:-Release}"
            build_project
            ;;
        "clean")
            clean_build
            if [[ "$2" == "build" ]]; then
                check_build_tools
                configure_cmake "${3:-Release}"
                build_project
            fi
            ;;
        "flash")
            flash_firmware
            ;;
        "check")
            check_build_tools
            log_success "Build environment check completed"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
