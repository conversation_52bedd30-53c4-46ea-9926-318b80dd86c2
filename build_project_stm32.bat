@echo off
setlocal enabledelayedexpansion

echo This script is used to build the STM32F4 DevKit project using CMake and ARM GCC
echo.
:: Use the current directory instead of hardcoded path
set "PROJECT_DIR=%~dp0"
cd /d "%PROJECT_DIR%"

:: Check if we're in the correct directory
if not exist "CMakeLists.txt" (
    echo Error: CMakeLists.txt not found. Please run this script from the project root directory.
    pause
    exit /b 1
)

:: Check if toolchain file exists
if not exist "gcc-arm-none-eabi.cmake" (
    echo Error: Toolchain file not found at gcc-arm-none-eabi.cmake
    pause
    exit /b 1
)

:: Check if MinGW is available
where mingw32-make >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Warning: mingw32-make not found in PATH
    echo.
    echo To fix this issue:
    echo 1. Install MinGW-w64 or MSYS2
    echo 2. Add MinGW bin directory to your PATH environment variable
    echo 3. Alternative: Use Visual Studio Developer Command Prompt
    echo.
    echo Continuing anyway - CMake will show specific error if needed...
    echo.
    pause
)

if not exist "build\" (
    mkdir build
)
echo Current directory: %cd%

:menu
cls
echo ===============================================
echo        STM32F4 DevKit Build System
echo ===============================================
echo.
echo 1. Clean Project
echo 2. Clean CMake Cache Only
echo 3. Build Debug Configuration
echo 4. Build Release Configuration
echo 5. Generate compile_commands.json only
echo 6. Show Build Information
echo 7. Check Build Environment
echo 8. Quit
echo.
set /p choice="Enter your choice (1-8): "

:: Trim spaces from the input
set "choice=!choice: =!"

if "%choice%"=="1" goto clean
if "%choice%"=="2" goto cleancache
if "%choice%"=="3" goto debug
if "%choice%"=="4" goto release
if "%choice%"=="5" goto compiledb
if "%choice%"=="6" goto info
if "%choice%"=="7" goto checkenv
if "%choice%"=="8" goto exit

echo Invalid choice. Please enter 1-8.
pause
goto menu

:clean
echo Cleaning Project...
if exist "build" (
    rmdir /s /q "build"
    mkdir build
)
echo Clean completed.
pause
goto menu

:cleancache
echo Cleaning CMake Cache...
if exist "build" (
    echo Removing CMake cache files...
    if exist "build\CMakeCache.txt" del /q "build\CMakeCache.txt"
    if exist "build\CMakeFiles" rmdir /s /q "build\CMakeFiles"
    if exist "build\cmake_install.cmake" del /q "build\cmake_install.cmake"
    echo CMake cache cleaned successfully.
    echo.
    echo Note: This will force CMake to re-detect compilers and toolchain.
    echo Use this if you're having toolchain detection issues.
) else (
    echo Build directory does not exist. Nothing to clean.
)
echo.
pause
goto menu

:debug
echo Configuring Debug Build...
cd build

:: Check if CMake cache exists and might have wrong compiler
if exist "CMakeCache.txt" (
    findstr /C:"CMAKE_C_COMPILER:FILEPATH=C:/MinGW" CMakeCache.txt >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo Detected MinGW compiler in cache, cleaning to force ARM GCC detection...
        if exist "CMakeCache.txt" del /q "CMakeCache.txt"
        if exist "CMakeFiles" rmdir /s /q "CMakeFiles"
        if exist "cmake_install.cmake" del /q "cmake_install.cmake"
        echo Cache cleaned.
        echo.
    )
)

cmake -G "MinGW Makefiles" ^
    -DCMAKE_BUILD_TYPE=Debug ^
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ^
    -DCMAKE_TOOLCHAIN_FILE=../gcc-arm-none-eabi.cmake ..
if %ERRORLEVEL% NEQ 0 (
    echo CMake configuration failed with error %ERRORLEVEL%
    echo.
    echo Possible solutions:
    echo - Install ARM GCC toolchain (STM32CubeCLT recommended)
    echo - Install MinGW-w64 or MSYS2 for build tools
    echo - Add ARM GCC to PATH
    echo - Use option 2 to clean CMake cache manually
    echo - Use option 7 to check build environment
    cd ..
    pause
    goto menu
)
goto build

:release
echo Configuring Release Build...
cd build

:: Check if CMake cache exists and might have wrong compiler
if exist "CMakeCache.txt" (
    findstr /C:"CMAKE_C_COMPILER:FILEPATH=C:/MinGW" CMakeCache.txt >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo Detected MinGW compiler in cache, cleaning to force ARM GCC detection...
        if exist "CMakeCache.txt" del /q "CMakeCache.txt"
        if exist "CMakeFiles" rmdir /s /q "CMakeFiles"
        if exist "cmake_install.cmake" del /q "cmake_install.cmake"
        echo Cache cleaned.
        echo.
    )
)

cmake -G "MinGW Makefiles" ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ^
    -DCMAKE_TOOLCHAIN_FILE=../gcc-arm-none-eabi.cmake ..
if %ERRORLEVEL% NEQ 0 (
    echo CMake configuration failed with error %ERRORLEVEL%
    echo.
    echo Possible solutions:
    echo - Install ARM GCC toolchain (STM32CubeCLT recommended)
    echo - Install MinGW-w64 or MSYS2 for build tools
    echo - Add ARM GCC to PATH
    echo - Use option 2 to clean CMake cache manually
    echo - Use option 7 to check build environment
    cd ..
    pause
    goto menu
)
goto build

:compiledb
echo Generating compile_commands.json...
cd build

:: Check if CMake cache exists and might have wrong compiler
if exist "CMakeCache.txt" (
    findstr /C:"CMAKE_C_COMPILER:FILEPATH=C:/MinGW" CMakeCache.txt >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo Detected MinGW compiler in cache, cleaning to force ARM GCC detection...
        if exist "CMakeCache.txt" del /q "CMakeCache.txt"
        if exist "CMakeFiles" rmdir /s /q "CMakeFiles"
        if exist "cmake_install.cmake" del /q "cmake_install.cmake"
        echo Cache cleaned.
        echo.
    )
)

cmake -G "MinGW Makefiles" ^
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ^
    -DCMAKE_TOOLCHAIN_FILE=../gcc-arm-none-eabi.cmake ..
if %ERRORLEVEL% NEQ 0 (
    echo CMake configuration failed with error %ERRORLEVEL%
    echo.
    echo Possible solutions:
    echo - Install ARM GCC toolchain (STM32CubeCLT recommended)
    echo - Install MinGW-w64 or MSYS2 for build tools
    echo - Add ARM GCC to PATH
    echo - Use option 2 to clean CMake cache manually
    echo - Use option 7 to check build environment
    cd ..
    pause
    goto menu
)
if exist "compile_commands.json" (
    copy /y "compile_commands.json" ".."
    echo Compilation database copied to project root.
) else (
    echo Warning: compile_commands.json was not generated.
)
cd ..
echo Compilation database generation completed.
pause
goto menu

:build
echo Building project...
mingw32-make -j%NUMBER_OF_PROCESSORS%
if %ERRORLEVEL% NEQ 0 (
    echo Build failed with error %ERRORLEVEL%
    echo.
    echo Possible issues:
    echo - ARM GCC toolchain not found in PATH or not in PATH
    echo - MinGW not installed or not in PATH
    echo - Missing dependencies
    echo - Configuration errors
    echo.
    echo Solutions:
    echo - Install MinGW-w64 or MSYS2
    echo - Add both MinGW and ARM GCC to PATH
    echo - Check toolchain installation
) else (
    echo Build completed successfully!
    echo.
    echo Generated files:
    if exist "STM32F4_DevKit.elf" echo - STM32F4_DevKit.elf
    if exist "STM32F4_DevKit.bin" echo - STM32F4_DevKit.bin
    if exist "STM32F4_DevKit.hex" echo - STM32F4_DevKit.hex
    if exist "STM32F4_DevKit.map" echo - STM32F4_DevKit.map
    echo.
    if exist "compile_commands.json" (
        copy /y "compile_commands.json" ".." >nul
        echo Compilation database copied to project root.
    )
)
cd ..
pause
goto menu

:checkenv
echo ===============================================
echo        Build Environment Check
echo ===============================================
echo.
echo Checking build tools...
echo.

:: Check CMake
where cmake >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] CMake found
    cmake --version | findstr /C:"cmake version"
) else (
    echo [ERROR] CMake not found in PATH
)
echo.

:: Check MinGW Make
where mingw32-make >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] MinGW Make found
    mingw32-make --version | findstr /C:"GNU Make"
) else (
    echo [ERROR] mingw32-make not found in PATH
)
echo.

:: Check ARM GCC
where arm-none-eabi-gcc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] ARM GCC found
    arm-none-eabi-gcc --version | findstr /C:"arm-none-eabi-gcc"
) else (
    echo [ERROR] arm-none-eabi-gcc not found in PATH
)
echo.

:: Check if toolchain path in cmake file exists
if exist "gcc-arm-none-eabi.cmake" (
    echo [OK] Toolchain file found
    findstr /C:"TOOLCHAIN_PATH" gcc-arm-none-eabi.cmake
) else (
    echo [ERROR] Toolchain file not found
)
echo.

echo Recommendations:
echo - Install STM32CubeCLT or standalone ARM GCC toolchain
echo - Install MinGW-w64 or MSYS2 for build tools
echo - Add both toolchains to your PATH environment variable
echo.
pause
goto menu

:info
echo ===============================================
echo           Build Information
echo ===============================================
echo.
echo Project: STM32F4 DevKit
echo Target MCU: STM32F407VGTx (Cortex-M4)
echo Toolchain: ARM GCC (arm-none-eabi)
echo Build System: CMake + Make
echo.
echo Project Structure:
echo - Application/     : Main application code
echo - Board_STM32F407/ : Board support package
echo - DeviceDrivers/   : Hardware abstraction layer
echo - DeviceInterfaces/: Device interface implementations
echo - Middleware/      : CMSIS and HAL libraries
echo - ThirdParty/      : External libraries (FreeRTOS, FATFS, etc.)
echo.
echo Build Outputs:
echo - STM32F4_DevKit.elf : Executable file
echo - STM32F4_DevKit.bin : Binary file for flashing
echo - STM32F4_DevKit.hex : Intel HEX file
echo - STM32F4_DevKit.map : Memory map file
echo.
if exist "build\STM32F4_DevKit.elf" (
    echo Last build: SUCCESS
) else (
    echo Last build: No build found
)
echo.
pause
goto menu

:exit
echo Exiting...
exit /b 0
