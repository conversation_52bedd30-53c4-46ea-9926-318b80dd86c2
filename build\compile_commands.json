[{"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM4 -DDEBUG -DSTM32F407xx -DUSE_HAL_DRIVER -D__FPU_PRESENT=1U -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Startup -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FreeRTOS\\include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FATFS\\src -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\USB_HOST\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -Wall -Wextra -g3 -O0 -o CMakeFiles\\STM32F4_DevKit.dir\\Application\\Src\\main.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Src\\main.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/main.c", "output": "CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM4 -DDEBUG -DSTM32F407xx -DUSE_HAL_DRIVER -D__FPU_PRESENT=1U -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Startup -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FreeRTOS\\include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FATFS\\src -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\USB_HOST\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -Wall -Wextra -g3 -O0 -o CMakeFiles\\STM32F4_DevKit.dir\\Application\\Src\\app.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Src\\app.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/app.c", "output": "CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM4 -DDEBUG -DSTM32F407xx -DUSE_HAL_DRIVER -D__FPU_PRESENT=1U -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Startup -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FreeRTOS\\include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FATFS\\src -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\USB_HOST\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -Wall -Wextra -g3 -O0 -o CMakeFiles\\STM32F4_DevKit.dir\\Application\\Src\\i2c_impl.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Src\\i2c_impl.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/i2c_impl.c", "output": "CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM4 -DDEBUG -DSTM32F407xx -DUSE_HAL_DRIVER -D__FPU_PRESENT=1U -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Startup -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FreeRTOS\\include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FATFS\\src -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\USB_HOST\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -Wall -Wextra -g3 -O0 -o CMakeFiles\\STM32F4_DevKit.dir\\Application\\Src\\uart_impl.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Src\\uart_impl.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/uart_impl.c", "output": "CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM4 -DDEBUG -DSTM32F407xx -DUSE_HAL_DRIVER -D__FPU_PRESENT=1U -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Startup -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FreeRTOS\\include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FATFS\\src -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\USB_HOST\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -Wall -Wextra -g3 -O0 -o CMakeFiles\\STM32F4_DevKit.dir\\Application\\Src\\usb_impl.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Src\\usb_impl.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/usb_impl.c", "output": "CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM4 -DDEBUG -DSTM32F407xx -DUSE_HAL_DRIVER -D__FPU_PRESENT=1U C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Startup C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FreeRTOS\\include C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\FATFS\\src C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\USB_HOST\\Inc C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -x assembler-with-cpp -g -Wall -Wextra -g3 -O0 -o CMakeFiles\\STM32F4_DevKit.dir\\Middleware\\CMSIS\\Device\\ST\\STM32F4xx\\Source\\Templates\\gcc\\startup_stm32f407xx.s.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Device\\ST\\STM32F4xx\\Source\\Templates\\gcc\\startup_stm32f407xx.s", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s", "output": "CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Board_STM32F407", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Startup -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -x assembler-with-cpp -g -o CMakeFiles\\board_stm32f407.dir\\__\\Middleware\\CMSIS\\Device\\ST\\STM32F4xx\\Source\\Templates\\gcc\\startup_stm32f407xx.s.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Device\\ST\\STM32F4xx\\Source\\Templates\\gcc\\startup_stm32f407xx.s", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s", "output": "Board_STM32F407/CMakeFiles/board_stm32f407.dir/__/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Board_STM32F407", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Startup -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\board_stm32f407.dir\\Src\\system.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Src\\system.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Board_STM32F407/Src/system.c", "output": "Board_STM32F407/CMakeFiles/board_stm32f407.dir/Src/system.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/DeviceDrivers/I2C", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\driver_i2c.dir\\Src\\i2c.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Src\\i2c.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/I2C/Src/i2c.c", "output": "DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/Src/i2c.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/DeviceDrivers/SPI", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\driver_spi.dir\\Src\\spi.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Src\\spi.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/SPI/Src/spi.c", "output": "DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/Src/spi.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/DeviceDrivers/USB", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\USB_HOST\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\driver_usb.dir\\Src\\usb.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Src\\usb.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/USB/Src/usb.c", "output": "DeviceDrivers/USB/CMakeFiles/driver_usb.dir/Src/usb.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/DeviceDrivers/UART", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Config -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\driver_uart.dir\\Src\\UART.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Src\\UART.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/UART/Src/UART.c", "output": "DeviceDrivers/UART/CMakeFiles/driver_uart.dir/Src/UART.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/DeviceInterfaces/IF_I2C", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\interface_i2c.dir\\Src\\i2c_interface.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Src\\i2c_interface.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_I2C/Src/i2c_interface.c", "output": "DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/Src/i2c_interface.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/DeviceInterfaces/IF_SPI", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\interface_spi.dir\\Src\\spi_interface.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Src\\spi_interface.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_SPI/Src/spi_interface.c", "output": "DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/Src/spi_interface.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/DeviceInterfaces/IF_USB", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\ThirdParty\\USB_HOST\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\interface_usb.dir\\Src\\usb_interface.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Src\\usb_interface.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_USB/Src/usb_interface.c", "output": "DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/Src/usb_interface.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/DeviceInterfaces/IF_UART", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\interface_uart.dir\\Src\\serial_interface.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Src\\serial_interface.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_UART/Src/serial_interface.c", "output": "DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/Src/serial_interface.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Application", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\app_src.dir\\Src\\app.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Src\\app.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/app.c", "output": "Application/CMakeFiles/app_src.dir/Src/app.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Application", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\app_src.dir\\Src\\main.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Src\\main.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/main.c", "output": "Application/CMakeFiles/app_src.dir/Src/main.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Application", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\app_src.dir\\Src\\i2c_impl.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Src\\i2c_impl.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/i2c_impl.c", "output": "Application/CMakeFiles/app_src.dir/Src/i2c_impl.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Application", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\app_src.dir\\Src\\uart_impl.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Src\\uart_impl.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/uart_impl.c", "output": "Application/CMakeFiles/app_src.dir/Src/uart_impl.c.obj"}, {"directory": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Application", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe  -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Board_STM32F407\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceDrivers\\USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_I2C\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_SPI\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_UART\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\DeviceInterfaces\\IF_USB\\Inc -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\CMSIS\\Include -IC:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Middleware\\STM32F4xx_HAL_Driver\\Inc -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -o CMakeFiles\\app_src.dir\\Src\\usb_impl.c.obj -c C:\\MyWorkSpaces\\ws_stm32\\STM32F4_DevKit\\Application\\Src\\usb_impl.c", "file": "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/usb_impl.c", "output": "Application/CMakeFiles/app_src.dir/Src/usb_impl.c.obj"}]