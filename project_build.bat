@echo off
REM STM32F4 DevKit Cross-Platform Build Script - Windows Wrapper
REM This script provides a Windows interface to the cross-platform build system
REM Supports both direct Windows builds and cross-platform bash builds

setlocal enabledelayedexpansion

REM Project configuration
set "PROJECT_NAME=STM32F4_DevKit"
set "PROJECT_DIR=%~dp0"
set "BUILD_DIR=%PROJECT_DIR%build"

REM Check if we're in the correct directory
if not exist "CMakeLists.txt" (
    echo Error: CMakeLists.txt not found. Please run this script from the project root directory.
    pause
    exit /b 1
)

REM Check if project_build.sh exists
if not exist "project_build.sh" (
    echo Warning: project_build.sh not found. Using Windows-only mode.
    set "BASH_MODE=0"
) else (
    set "BASH_MODE=1"
)

REM Show usage if help requested
if "%1"=="help" goto :show_usage
if "%1"=="-h" goto :show_usage
if "%1"=="--help" goto :show_usage

REM If arguments provided, try to handle them
if not "%1"=="" (
    if "%BASH_MODE%"=="1" (
        goto :try_bash_mode
    ) else (
        goto :windows_direct_mode
    )
)

REM Interactive menu mode
:menu
cls
echo ===============================================
echo      STM32F4 DevKit Build System (Windows)
echo ===============================================
echo.
echo 1. Build Release (Cross-platform via Bash)
echo 2. Build Debug (Cross-platform via Bash)
echo 3. Build Release (Windows Native)
echo 4. Build Debug (Windows Native)
echo 5. Clean Build Directory
echo 6. Check Build Environment
echo 7. Flash Firmware
echo 8. Show Help
echo 9. Exit
echo.
set /p choice="Enter your choice (1-9): "

if "%choice%"=="1" goto :bash_build_release
if "%choice%"=="2" goto :bash_build_debug
if "%choice%"=="3" goto :windows_build_release
if "%choice%"=="4" goto :windows_build_debug
if "%choice%"=="5" goto :clean_build
if "%choice%"=="6" goto :check_environment
if "%choice%"=="7" goto :flash_firmware
if "%choice%"=="8" goto :show_usage
if "%choice%"=="9" goto :exit

echo Invalid choice. Please enter 1-9.
pause
goto :menu

REM Try to find bash (Git Bash, MSYS2, WSL)
:find_bash
set "BASH_CMD="

REM Check for Git Bash (multiple possible locations)
if exist "C:\Program Files\Git\bin\bash.exe" (
    set "BASH_CMD=C:\Program Files\Git\bin\bash.exe"
    goto :found_bash
)
if exist "C:\Program Files (x86)\Git\bin\bash.exe" (
    set "BASH_CMD=C:\Program Files (x86)\Git\bin\bash.exe"
    goto :found_bash
)

REM Check for MSYS2 (multiple possible locations)
if exist "C:\msys64\usr\bin\bash.exe" (
    set "BASH_CMD=C:\msys64\usr\bin\bash.exe"
    goto :found_bash
)
if exist "C:\msys32\usr\bin\bash.exe" (
    set "BASH_CMD=C:\msys32\usr\bin\bash.exe"
    goto :found_bash
)

REM Check if bash is in PATH
where bash >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    set "BASH_CMD=bash"
    goto :found_bash
)

REM Check for WSL
where wsl >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    set "BASH_CMD=wsl bash"
    goto :found_bash
)

REM Bash not found
echo Error: Bash not found!
echo.
echo Please install one of the following:
echo 1. Git for Windows (includes Git Bash)
echo 2. MSYS2
echo 3. Windows Subsystem for Linux (WSL)
echo.
echo Falling back to Windows native build...
pause
goto :windows_direct_mode

:found_bash
echo Using bash: %BASH_CMD%
echo.
goto :execute_bash_script

REM Bash mode handlers
:try_bash_mode
call :find_bash
if "%BASH_CMD%"=="" (
    echo Bash not available, using Windows native mode...
    goto :windows_direct_mode
)
goto :execute_bash_script

:bash_build_release
call :find_bash
if "%BASH_CMD%"=="" goto :bash_not_found_menu
call :convert_path_for_bash
%BASH_CMD% "%SCRIPT_PATH%" build release
goto :build_complete

:bash_build_debug
call :find_bash
if "%BASH_CMD%"=="" goto :bash_not_found_menu
call :convert_path_for_bash
%BASH_CMD% "%SCRIPT_PATH%" build debug
goto :build_complete

:execute_bash_script
call :convert_path_for_bash
REM Execute the shell script with all arguments
%BASH_CMD% "%SCRIPT_PATH%" %*

REM Check exit code
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Build script failed with exit code %ERRORLEVEL%
    echo.
    echo If you encounter issues with the cross-platform script,
    echo you can use the native Windows build script: build-gcc-arm-none-eabi.bat
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo Build script completed successfully!
pause
goto :exit

:bash_not_found_menu
echo.
echo Bash not found! Please install Git for Windows, MSYS2, or WSL.
echo Returning to menu...
pause
goto :menu

REM Windows native build functions
:windows_direct_mode
if "%1"=="build" (
    if "%2"=="debug" (
        goto :windows_build_debug
    ) else (
        goto :windows_build_release
    )
)
if "%1"=="clean" goto :clean_build
if "%1"=="check" goto :check_environment
if "%1"=="flash" goto :flash_firmware
echo Unknown command: %1
goto :show_usage

:windows_build_release
echo Building Release version (Windows Native)...
call :ensure_build_dir
cd /d "%BUILD_DIR%"
cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release -DCMAKE_EXPORT_COMPILE_COMMANDS=ON -DCMAKE_TOOLCHAIN_FILE=../Scripts/gcc-arm-none-eabi.cmake ..
if %ERRORLEVEL% NEQ 0 (
    echo CMake configuration failed!
    pause
    goto :menu
)
mingw32-make -j%NUMBER_OF_PROCESSORS%
if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    goto :menu
)
call :show_build_artifacts
goto :build_complete

:windows_build_debug
echo Building Debug version (Windows Native)...
call :ensure_build_dir
cd /d "%BUILD_DIR%"
cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug -DCMAKE_EXPORT_COMPILE_COMMANDS=ON -DCMAKE_TOOLCHAIN_FILE=../Scripts/gcc-arm-none-eabi.cmake ..
if %ERRORLEVEL% NEQ 0 (
    echo CMake configuration failed!
    pause
    goto :menu
)
mingw32-make -j%NUMBER_OF_PROCESSORS%
if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    goto :menu
)
call :show_build_artifacts
goto :build_complete

:clean_build
echo Cleaning build directory...
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%"
)
mkdir "%BUILD_DIR%"
echo Build directory cleaned.
if not "%1"=="" (
    pause
    goto :menu
)
goto :build_complete

:ensure_build_dir
if not exist "%BUILD_DIR%" (
    mkdir "%BUILD_DIR%"
)
cd /d "%PROJECT_DIR%"
goto :eof

:convert_path_for_bash
REM Convert Windows path to Unix-style path for bash
set "SCRIPT_PATH=%~dp0project_build.sh"
set "SCRIPT_PATH=%SCRIPT_PATH:\=/%"
set "SCRIPT_PATH=%SCRIPT_PATH:C:=/c%"
set "SCRIPT_PATH=%SCRIPT_PATH:D:=/d%"
set "SCRIPT_PATH=%SCRIPT_PATH:E:=/e%"
goto :eof

:show_build_artifacts
echo.
echo Build Artifacts:
cd /d "%BUILD_DIR%"
if exist "%PROJECT_NAME%.elf" (
    echo   [OK] %PROJECT_NAME%.elf
    arm-none-eabi-size "%PROJECT_NAME%.elf" 2>nul
)
if exist "%PROJECT_NAME%.bin" echo   [OK] %PROJECT_NAME%.bin
if exist "%PROJECT_NAME%.hex" echo   [OK] %PROJECT_NAME%.hex
if exist "%PROJECT_NAME%.map" echo   [OK] %PROJECT_NAME%.map
if exist "compile_commands.json" echo   [OK] compile_commands.json
cd /d "%PROJECT_DIR%"
goto :eof

:check_environment
echo Checking build environment...
echo.

REM Check CMake
where cmake >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] CMake found
    cmake --version | findstr "cmake version"
) else (
    echo [ERROR] CMake not found
)

REM Check MinGW Make
where mingw32-make >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] MinGW Make found
) else (
    echo [ERROR] mingw32-make not found
)

REM Check ARM GCC
where arm-none-eabi-gcc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] ARM GCC found
    arm-none-eabi-gcc --version | findstr "arm-none-eabi-gcc"
) else (
    echo [ERROR] arm-none-eabi-gcc not found
)

REM Check Bash availability
call :find_bash
if not "%BASH_CMD%"=="" (
    echo [OK] Bash found: %BASH_CMD%
) else (
    echo [WARNING] Bash not found - cross-platform features unavailable
)

echo.
echo Environment check completed.
if not "%1"=="" (
    pause
    goto :menu
)
goto :eof

:flash_firmware
echo Checking for flashing tools...
where st-flash >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Flashing with st-flash...
    st-flash write "%BUILD_DIR%\%PROJECT_NAME%.bin" 0x8000000
) else (
    echo st-flash not found.
    echo Please use STM32CubeProgrammer or other flashing tools.
    echo Binary file: %BUILD_DIR%\%PROJECT_NAME%.bin
)
if not "%1"=="" (
    pause
    goto :menu
)
goto :eof

:show_usage
echo.
echo STM32F4 DevKit Cross-Platform Build Script (Windows)
echo =====================================================
echo.
echo Usage: %~nx0 [COMMAND] [OPTIONS]
echo.
echo Commands:
echo   build [debug^|release]  - Build the project (default: release)
echo   clean                  - Clean build directory
echo   check                  - Check build environment
echo   flash                  - Flash firmware to device
echo   help                   - Show this help
echo.
echo Interactive Mode:
echo   Run without arguments to enter interactive menu
echo.
echo Examples:
echo   %~nx0                  - Interactive menu
echo   %~nx0 build            - Build release version
echo   %~nx0 build debug      - Build debug version
echo   %~nx0 clean            - Clean build directory
echo   %~nx0 check            - Check environment
echo.
echo Build Modes:
echo   - Cross-platform: Uses bash and project_build.sh (if available)
echo   - Windows Native: Uses Windows tools directly
echo.
if not "%1"=="" (
    pause
    goto :menu
)
goto :eof

:build_complete
echo.
echo ===============================================
echo Build operation completed!
echo ===============================================
if not "%1"=="" (
    echo.
    echo Press any key to return to menu...
    pause >nul
    goto :menu
)
goto :eof

:exit
echo.
echo Goodbye!
exit /b 0
