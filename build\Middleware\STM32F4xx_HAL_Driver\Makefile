# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe

# The command to remove a file.
RM = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake-gui.exe -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver\\CMakeFiles\progress.marks
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Middleware/STM32F4xx_HAL_Driver/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Middleware/STM32F4xx_HAL_Driver/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Middleware/STM32F4xx_HAL_Driver/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Middleware/STM32F4xx_HAL_Driver/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/rule:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/rule
.PHONY : Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/rule

# Convenience name for target.
stm32f4xx_hal: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/rule
.PHONY : stm32f4xx_hal

# fast build rule for target.
stm32f4xx_hal/fast:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/build
.PHONY : stm32f4xx_hal/fast

Src/stm32f4xx_hal.obj: Src/stm32f4xx_hal.c.obj
.PHONY : Src/stm32f4xx_hal.obj

# target to build an object file
Src/stm32f4xx_hal.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.obj
.PHONY : Src/stm32f4xx_hal.c.obj

Src/stm32f4xx_hal.i: Src/stm32f4xx_hal.c.i
.PHONY : Src/stm32f4xx_hal.i

# target to preprocess a source file
Src/stm32f4xx_hal.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.i
.PHONY : Src/stm32f4xx_hal.c.i

Src/stm32f4xx_hal.s: Src/stm32f4xx_hal.c.s
.PHONY : Src/stm32f4xx_hal.s

# target to generate assembly for a file
Src/stm32f4xx_hal.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.s
.PHONY : Src/stm32f4xx_hal.c.s

Src/stm32f4xx_hal_adc.obj: Src/stm32f4xx_hal_adc.c.obj
.PHONY : Src/stm32f4xx_hal_adc.obj

# target to build an object file
Src/stm32f4xx_hal_adc.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.obj
.PHONY : Src/stm32f4xx_hal_adc.c.obj

Src/stm32f4xx_hal_adc.i: Src/stm32f4xx_hal_adc.c.i
.PHONY : Src/stm32f4xx_hal_adc.i

# target to preprocess a source file
Src/stm32f4xx_hal_adc.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.i
.PHONY : Src/stm32f4xx_hal_adc.c.i

Src/stm32f4xx_hal_adc.s: Src/stm32f4xx_hal_adc.c.s
.PHONY : Src/stm32f4xx_hal_adc.s

# target to generate assembly for a file
Src/stm32f4xx_hal_adc.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.s
.PHONY : Src/stm32f4xx_hal_adc.c.s

Src/stm32f4xx_hal_adc_ex.obj: Src/stm32f4xx_hal_adc_ex.c.obj
.PHONY : Src/stm32f4xx_hal_adc_ex.obj

# target to build an object file
Src/stm32f4xx_hal_adc_ex.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.obj
.PHONY : Src/stm32f4xx_hal_adc_ex.c.obj

Src/stm32f4xx_hal_adc_ex.i: Src/stm32f4xx_hal_adc_ex.c.i
.PHONY : Src/stm32f4xx_hal_adc_ex.i

# target to preprocess a source file
Src/stm32f4xx_hal_adc_ex.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.i
.PHONY : Src/stm32f4xx_hal_adc_ex.c.i

Src/stm32f4xx_hal_adc_ex.s: Src/stm32f4xx_hal_adc_ex.c.s
.PHONY : Src/stm32f4xx_hal_adc_ex.s

# target to generate assembly for a file
Src/stm32f4xx_hal_adc_ex.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.s
.PHONY : Src/stm32f4xx_hal_adc_ex.c.s

Src/stm32f4xx_hal_cortex.obj: Src/stm32f4xx_hal_cortex.c.obj
.PHONY : Src/stm32f4xx_hal_cortex.obj

# target to build an object file
Src/stm32f4xx_hal_cortex.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.obj
.PHONY : Src/stm32f4xx_hal_cortex.c.obj

Src/stm32f4xx_hal_cortex.i: Src/stm32f4xx_hal_cortex.c.i
.PHONY : Src/stm32f4xx_hal_cortex.i

# target to preprocess a source file
Src/stm32f4xx_hal_cortex.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.i
.PHONY : Src/stm32f4xx_hal_cortex.c.i

Src/stm32f4xx_hal_cortex.s: Src/stm32f4xx_hal_cortex.c.s
.PHONY : Src/stm32f4xx_hal_cortex.s

# target to generate assembly for a file
Src/stm32f4xx_hal_cortex.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.s
.PHONY : Src/stm32f4xx_hal_cortex.c.s

Src/stm32f4xx_hal_dma.obj: Src/stm32f4xx_hal_dma.c.obj
.PHONY : Src/stm32f4xx_hal_dma.obj

# target to build an object file
Src/stm32f4xx_hal_dma.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.obj
.PHONY : Src/stm32f4xx_hal_dma.c.obj

Src/stm32f4xx_hal_dma.i: Src/stm32f4xx_hal_dma.c.i
.PHONY : Src/stm32f4xx_hal_dma.i

# target to preprocess a source file
Src/stm32f4xx_hal_dma.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.i
.PHONY : Src/stm32f4xx_hal_dma.c.i

Src/stm32f4xx_hal_dma.s: Src/stm32f4xx_hal_dma.c.s
.PHONY : Src/stm32f4xx_hal_dma.s

# target to generate assembly for a file
Src/stm32f4xx_hal_dma.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.s
.PHONY : Src/stm32f4xx_hal_dma.c.s

Src/stm32f4xx_hal_flash.obj: Src/stm32f4xx_hal_flash.c.obj
.PHONY : Src/stm32f4xx_hal_flash.obj

# target to build an object file
Src/stm32f4xx_hal_flash.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.obj
.PHONY : Src/stm32f4xx_hal_flash.c.obj

Src/stm32f4xx_hal_flash.i: Src/stm32f4xx_hal_flash.c.i
.PHONY : Src/stm32f4xx_hal_flash.i

# target to preprocess a source file
Src/stm32f4xx_hal_flash.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.i
.PHONY : Src/stm32f4xx_hal_flash.c.i

Src/stm32f4xx_hal_flash.s: Src/stm32f4xx_hal_flash.c.s
.PHONY : Src/stm32f4xx_hal_flash.s

# target to generate assembly for a file
Src/stm32f4xx_hal_flash.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.s
.PHONY : Src/stm32f4xx_hal_flash.c.s

Src/stm32f4xx_hal_flash_ex.obj: Src/stm32f4xx_hal_flash_ex.c.obj
.PHONY : Src/stm32f4xx_hal_flash_ex.obj

# target to build an object file
Src/stm32f4xx_hal_flash_ex.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.obj
.PHONY : Src/stm32f4xx_hal_flash_ex.c.obj

Src/stm32f4xx_hal_flash_ex.i: Src/stm32f4xx_hal_flash_ex.c.i
.PHONY : Src/stm32f4xx_hal_flash_ex.i

# target to preprocess a source file
Src/stm32f4xx_hal_flash_ex.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.i
.PHONY : Src/stm32f4xx_hal_flash_ex.c.i

Src/stm32f4xx_hal_flash_ex.s: Src/stm32f4xx_hal_flash_ex.c.s
.PHONY : Src/stm32f4xx_hal_flash_ex.s

# target to generate assembly for a file
Src/stm32f4xx_hal_flash_ex.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.s
.PHONY : Src/stm32f4xx_hal_flash_ex.c.s

Src/stm32f4xx_hal_gpio.obj: Src/stm32f4xx_hal_gpio.c.obj
.PHONY : Src/stm32f4xx_hal_gpio.obj

# target to build an object file
Src/stm32f4xx_hal_gpio.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.obj
.PHONY : Src/stm32f4xx_hal_gpio.c.obj

Src/stm32f4xx_hal_gpio.i: Src/stm32f4xx_hal_gpio.c.i
.PHONY : Src/stm32f4xx_hal_gpio.i

# target to preprocess a source file
Src/stm32f4xx_hal_gpio.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.i
.PHONY : Src/stm32f4xx_hal_gpio.c.i

Src/stm32f4xx_hal_gpio.s: Src/stm32f4xx_hal_gpio.c.s
.PHONY : Src/stm32f4xx_hal_gpio.s

# target to generate assembly for a file
Src/stm32f4xx_hal_gpio.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.s
.PHONY : Src/stm32f4xx_hal_gpio.c.s

Src/stm32f4xx_hal_i2c.obj: Src/stm32f4xx_hal_i2c.c.obj
.PHONY : Src/stm32f4xx_hal_i2c.obj

# target to build an object file
Src/stm32f4xx_hal_i2c.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.obj
.PHONY : Src/stm32f4xx_hal_i2c.c.obj

Src/stm32f4xx_hal_i2c.i: Src/stm32f4xx_hal_i2c.c.i
.PHONY : Src/stm32f4xx_hal_i2c.i

# target to preprocess a source file
Src/stm32f4xx_hal_i2c.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.i
.PHONY : Src/stm32f4xx_hal_i2c.c.i

Src/stm32f4xx_hal_i2c.s: Src/stm32f4xx_hal_i2c.c.s
.PHONY : Src/stm32f4xx_hal_i2c.s

# target to generate assembly for a file
Src/stm32f4xx_hal_i2c.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.s
.PHONY : Src/stm32f4xx_hal_i2c.c.s

Src/stm32f4xx_hal_pwr.obj: Src/stm32f4xx_hal_pwr.c.obj
.PHONY : Src/stm32f4xx_hal_pwr.obj

# target to build an object file
Src/stm32f4xx_hal_pwr.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.obj
.PHONY : Src/stm32f4xx_hal_pwr.c.obj

Src/stm32f4xx_hal_pwr.i: Src/stm32f4xx_hal_pwr.c.i
.PHONY : Src/stm32f4xx_hal_pwr.i

# target to preprocess a source file
Src/stm32f4xx_hal_pwr.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.i
.PHONY : Src/stm32f4xx_hal_pwr.c.i

Src/stm32f4xx_hal_pwr.s: Src/stm32f4xx_hal_pwr.c.s
.PHONY : Src/stm32f4xx_hal_pwr.s

# target to generate assembly for a file
Src/stm32f4xx_hal_pwr.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.s
.PHONY : Src/stm32f4xx_hal_pwr.c.s

Src/stm32f4xx_hal_rcc.obj: Src/stm32f4xx_hal_rcc.c.obj
.PHONY : Src/stm32f4xx_hal_rcc.obj

# target to build an object file
Src/stm32f4xx_hal_rcc.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.obj
.PHONY : Src/stm32f4xx_hal_rcc.c.obj

Src/stm32f4xx_hal_rcc.i: Src/stm32f4xx_hal_rcc.c.i
.PHONY : Src/stm32f4xx_hal_rcc.i

# target to preprocess a source file
Src/stm32f4xx_hal_rcc.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.i
.PHONY : Src/stm32f4xx_hal_rcc.c.i

Src/stm32f4xx_hal_rcc.s: Src/stm32f4xx_hal_rcc.c.s
.PHONY : Src/stm32f4xx_hal_rcc.s

# target to generate assembly for a file
Src/stm32f4xx_hal_rcc.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.s
.PHONY : Src/stm32f4xx_hal_rcc.c.s

Src/stm32f4xx_hal_spi.obj: Src/stm32f4xx_hal_spi.c.obj
.PHONY : Src/stm32f4xx_hal_spi.obj

# target to build an object file
Src/stm32f4xx_hal_spi.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.obj
.PHONY : Src/stm32f4xx_hal_spi.c.obj

Src/stm32f4xx_hal_spi.i: Src/stm32f4xx_hal_spi.c.i
.PHONY : Src/stm32f4xx_hal_spi.i

# target to preprocess a source file
Src/stm32f4xx_hal_spi.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.i
.PHONY : Src/stm32f4xx_hal_spi.c.i

Src/stm32f4xx_hal_spi.s: Src/stm32f4xx_hal_spi.c.s
.PHONY : Src/stm32f4xx_hal_spi.s

# target to generate assembly for a file
Src/stm32f4xx_hal_spi.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.s
.PHONY : Src/stm32f4xx_hal_spi.c.s

Src/stm32f4xx_hal_tim.obj: Src/stm32f4xx_hal_tim.c.obj
.PHONY : Src/stm32f4xx_hal_tim.obj

# target to build an object file
Src/stm32f4xx_hal_tim.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.obj
.PHONY : Src/stm32f4xx_hal_tim.c.obj

Src/stm32f4xx_hal_tim.i: Src/stm32f4xx_hal_tim.c.i
.PHONY : Src/stm32f4xx_hal_tim.i

# target to preprocess a source file
Src/stm32f4xx_hal_tim.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.i
.PHONY : Src/stm32f4xx_hal_tim.c.i

Src/stm32f4xx_hal_tim.s: Src/stm32f4xx_hal_tim.c.s
.PHONY : Src/stm32f4xx_hal_tim.s

# target to generate assembly for a file
Src/stm32f4xx_hal_tim.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.s
.PHONY : Src/stm32f4xx_hal_tim.c.s

Src/stm32f4xx_hal_tim_ex.obj: Src/stm32f4xx_hal_tim_ex.c.obj
.PHONY : Src/stm32f4xx_hal_tim_ex.obj

# target to build an object file
Src/stm32f4xx_hal_tim_ex.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.obj
.PHONY : Src/stm32f4xx_hal_tim_ex.c.obj

Src/stm32f4xx_hal_tim_ex.i: Src/stm32f4xx_hal_tim_ex.c.i
.PHONY : Src/stm32f4xx_hal_tim_ex.i

# target to preprocess a source file
Src/stm32f4xx_hal_tim_ex.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.i
.PHONY : Src/stm32f4xx_hal_tim_ex.c.i

Src/stm32f4xx_hal_tim_ex.s: Src/stm32f4xx_hal_tim_ex.c.s
.PHONY : Src/stm32f4xx_hal_tim_ex.s

# target to generate assembly for a file
Src/stm32f4xx_hal_tim_ex.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.s
.PHONY : Src/stm32f4xx_hal_tim_ex.c.s

Src/stm32f4xx_hal_uart.obj: Src/stm32f4xx_hal_uart.c.obj
.PHONY : Src/stm32f4xx_hal_uart.obj

# target to build an object file
Src/stm32f4xx_hal_uart.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.obj
.PHONY : Src/stm32f4xx_hal_uart.c.obj

Src/stm32f4xx_hal_uart.i: Src/stm32f4xx_hal_uart.c.i
.PHONY : Src/stm32f4xx_hal_uart.i

# target to preprocess a source file
Src/stm32f4xx_hal_uart.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.i
.PHONY : Src/stm32f4xx_hal_uart.c.i

Src/stm32f4xx_hal_uart.s: Src/stm32f4xx_hal_uart.c.s
.PHONY : Src/stm32f4xx_hal_uart.s

# target to generate assembly for a file
Src/stm32f4xx_hal_uart.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.s
.PHONY : Src/stm32f4xx_hal_uart.c.s

Src/stm32f4xx_hal_usart.obj: Src/stm32f4xx_hal_usart.c.obj
.PHONY : Src/stm32f4xx_hal_usart.obj

# target to build an object file
Src/stm32f4xx_hal_usart.c.obj:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.obj
.PHONY : Src/stm32f4xx_hal_usart.c.obj

Src/stm32f4xx_hal_usart.i: Src/stm32f4xx_hal_usart.c.i
.PHONY : Src/stm32f4xx_hal_usart.i

# target to preprocess a source file
Src/stm32f4xx_hal_usart.c.i:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.i
.PHONY : Src/stm32f4xx_hal_usart.c.i

Src/stm32f4xx_hal_usart.s: Src/stm32f4xx_hal_usart.c.s
.PHONY : Src/stm32f4xx_hal_usart.s

# target to generate assembly for a file
Src/stm32f4xx_hal_usart.c.s:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(MAKE) $(MAKESILENT) -f Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\build.make Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.s
.PHONY : Src/stm32f4xx_hal_usart.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... stm32f4xx_hal
	@echo ... Src/stm32f4xx_hal.obj
	@echo ... Src/stm32f4xx_hal.i
	@echo ... Src/stm32f4xx_hal.s
	@echo ... Src/stm32f4xx_hal_adc.obj
	@echo ... Src/stm32f4xx_hal_adc.i
	@echo ... Src/stm32f4xx_hal_adc.s
	@echo ... Src/stm32f4xx_hal_adc_ex.obj
	@echo ... Src/stm32f4xx_hal_adc_ex.i
	@echo ... Src/stm32f4xx_hal_adc_ex.s
	@echo ... Src/stm32f4xx_hal_cortex.obj
	@echo ... Src/stm32f4xx_hal_cortex.i
	@echo ... Src/stm32f4xx_hal_cortex.s
	@echo ... Src/stm32f4xx_hal_dma.obj
	@echo ... Src/stm32f4xx_hal_dma.i
	@echo ... Src/stm32f4xx_hal_dma.s
	@echo ... Src/stm32f4xx_hal_flash.obj
	@echo ... Src/stm32f4xx_hal_flash.i
	@echo ... Src/stm32f4xx_hal_flash.s
	@echo ... Src/stm32f4xx_hal_flash_ex.obj
	@echo ... Src/stm32f4xx_hal_flash_ex.i
	@echo ... Src/stm32f4xx_hal_flash_ex.s
	@echo ... Src/stm32f4xx_hal_gpio.obj
	@echo ... Src/stm32f4xx_hal_gpio.i
	@echo ... Src/stm32f4xx_hal_gpio.s
	@echo ... Src/stm32f4xx_hal_i2c.obj
	@echo ... Src/stm32f4xx_hal_i2c.i
	@echo ... Src/stm32f4xx_hal_i2c.s
	@echo ... Src/stm32f4xx_hal_pwr.obj
	@echo ... Src/stm32f4xx_hal_pwr.i
	@echo ... Src/stm32f4xx_hal_pwr.s
	@echo ... Src/stm32f4xx_hal_rcc.obj
	@echo ... Src/stm32f4xx_hal_rcc.i
	@echo ... Src/stm32f4xx_hal_rcc.s
	@echo ... Src/stm32f4xx_hal_spi.obj
	@echo ... Src/stm32f4xx_hal_spi.i
	@echo ... Src/stm32f4xx_hal_spi.s
	@echo ... Src/stm32f4xx_hal_tim.obj
	@echo ... Src/stm32f4xx_hal_tim.i
	@echo ... Src/stm32f4xx_hal_tim.s
	@echo ... Src/stm32f4xx_hal_tim_ex.obj
	@echo ... Src/stm32f4xx_hal_tim_ex.i
	@echo ... Src/stm32f4xx_hal_tim_ex.s
	@echo ... Src/stm32f4xx_hal_uart.obj
	@echo ... Src/stm32f4xx_hal_uart.i
	@echo ... Src/stm32f4xx_hal_uart.s
	@echo ... Src/stm32f4xx_hal_usart.obj
	@echo ... Src/stm32f4xx_hal_usart.i
	@echo ... Src/stm32f4xx_hal_usart.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

