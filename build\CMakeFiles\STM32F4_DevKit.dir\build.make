# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe

# The command to remove a file.
RM = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build

# Include any dependencies generated for this target.
include CMakeFiles/STM32F4_DevKit.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/STM32F4_DevKit.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/STM32F4_DevKit.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/STM32F4_DevKit.dir/flags.make

CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj: CMakeFiles/STM32F4_DevKit.dir/flags.make
CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/main.c
CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj: CMakeFiles/STM32F4_DevKit.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj -MF CMakeFiles\STM32F4_DevKit.dir\Application\Src\main.c.obj.d -o CMakeFiles\STM32F4_DevKit.dir\Application\Src\main.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\main.c

CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.i"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\main.c > CMakeFiles\STM32F4_DevKit.dir\Application\Src\main.c.i

CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.s"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\main.c -o CMakeFiles\STM32F4_DevKit.dir\Application\Src\main.c.s

CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj: CMakeFiles/STM32F4_DevKit.dir/flags.make
CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/app.c
CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj: CMakeFiles/STM32F4_DevKit.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj -MF CMakeFiles\STM32F4_DevKit.dir\Application\Src\app.c.obj.d -o CMakeFiles\STM32F4_DevKit.dir\Application\Src\app.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\app.c

CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.i"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\app.c > CMakeFiles\STM32F4_DevKit.dir\Application\Src\app.c.i

CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.s"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\app.c -o CMakeFiles\STM32F4_DevKit.dir\Application\Src\app.c.s

CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj: CMakeFiles/STM32F4_DevKit.dir/flags.make
CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/i2c_impl.c
CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj: CMakeFiles/STM32F4_DevKit.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj -MF CMakeFiles\STM32F4_DevKit.dir\Application\Src\i2c_impl.c.obj.d -o CMakeFiles\STM32F4_DevKit.dir\Application\Src\i2c_impl.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\i2c_impl.c

CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.i"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\i2c_impl.c > CMakeFiles\STM32F4_DevKit.dir\Application\Src\i2c_impl.c.i

CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.s"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\i2c_impl.c -o CMakeFiles\STM32F4_DevKit.dir\Application\Src\i2c_impl.c.s

CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj: CMakeFiles/STM32F4_DevKit.dir/flags.make
CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/uart_impl.c
CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj: CMakeFiles/STM32F4_DevKit.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj -MF CMakeFiles\STM32F4_DevKit.dir\Application\Src\uart_impl.c.obj.d -o CMakeFiles\STM32F4_DevKit.dir\Application\Src\uart_impl.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\uart_impl.c

CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.i"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\uart_impl.c > CMakeFiles\STM32F4_DevKit.dir\Application\Src\uart_impl.c.i

CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.s"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\uart_impl.c -o CMakeFiles\STM32F4_DevKit.dir\Application\Src\uart_impl.c.s

CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj: CMakeFiles/STM32F4_DevKit.dir/flags.make
CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/Src/usb_impl.c
CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj: CMakeFiles/STM32F4_DevKit.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj -MF CMakeFiles\STM32F4_DevKit.dir\Application\Src\usb_impl.c.obj.d -o CMakeFiles\STM32F4_DevKit.dir\Application\Src\usb_impl.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\usb_impl.c

CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.i"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\usb_impl.c > CMakeFiles\STM32F4_DevKit.dir\Application\Src\usb_impl.c.i

CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.s"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Src\usb_impl.c -o CMakeFiles\STM32F4_DevKit.dir\Application\Src\usb_impl.c.s

CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj: CMakeFiles/STM32F4_DevKit.dir/flags.make
CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building ASM object CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -o CMakeFiles\STM32F4_DevKit.dir\Middleware\CMSIS\Device\ST\STM32F4xx\Source\Templates\gcc\startup_stm32f407xx.s.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS\Device\ST\STM32F4xx\Source\Templates\gcc\startup_stm32f407xx.s

CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing ASM source to CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_ASM_CREATE_PREPROCESSED_SOURCE

CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling ASM source to assembly CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_ASM_CREATE_ASSEMBLY_SOURCE

# Object files for target STM32F4_DevKit
STM32F4_DevKit_OBJECTS = \
"CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj" \
"CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj" \
"CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj" \
"CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj" \
"CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj" \
"CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj"

# External object files for target STM32F4_DevKit
STM32F4_DevKit_EXTERNAL_OBJECTS = \
"C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Application/CMakeFiles/app_src.dir/Src/app.c.obj" \
"C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Application/CMakeFiles/app_src.dir/Src/main.c.obj" \
"C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Application/CMakeFiles/app_src.dir/Src/i2c_impl.c.obj" \
"C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Application/CMakeFiles/app_src.dir/Src/uart_impl.c.obj" \
"C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Application/CMakeFiles/app_src.dir/Src/usb_impl.c.obj"

bin/STM32F4_DevKit.elf: CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj
bin/STM32F4_DevKit.elf: CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj
bin/STM32F4_DevKit.elf: CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj
bin/STM32F4_DevKit.elf: CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj
bin/STM32F4_DevKit.elf: CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj
bin/STM32F4_DevKit.elf: CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj
bin/STM32F4_DevKit.elf: Application/CMakeFiles/app_src.dir/Src/app.c.obj
bin/STM32F4_DevKit.elf: Application/CMakeFiles/app_src.dir/Src/main.c.obj
bin/STM32F4_DevKit.elf: Application/CMakeFiles/app_src.dir/Src/i2c_impl.c.obj
bin/STM32F4_DevKit.elf: Application/CMakeFiles/app_src.dir/Src/uart_impl.c.obj
bin/STM32F4_DevKit.elf: Application/CMakeFiles/app_src.dir/Src/usb_impl.c.obj
bin/STM32F4_DevKit.elf: CMakeFiles/STM32F4_DevKit.dir/build.make
bin/STM32F4_DevKit.elf: lib/libboard_stm32f407.a
bin/STM32F4_DevKit.elf: lib/libcmsis.a
bin/STM32F4_DevKit.elf: lib/libstm32f4xx_hal.a
bin/STM32F4_DevKit.elf: lib/libdriver_i2c.a
bin/STM32F4_DevKit.elf: lib/libdriver_spi.a
bin/STM32F4_DevKit.elf: lib/libdriver_uart.a
bin/STM32F4_DevKit.elf: lib/libdriver_usb.a
bin/STM32F4_DevKit.elf: lib/libinterface_i2c.a
bin/STM32F4_DevKit.elf: lib/libinterface_spi.a
bin/STM32F4_DevKit.elf: lib/libinterface_uart.a
bin/STM32F4_DevKit.elf: lib/libinterface_usb.a
bin/STM32F4_DevKit.elf: lib/libdriver_i2c.a
bin/STM32F4_DevKit.elf: lib/libdriver_spi.a
bin/STM32F4_DevKit.elf: lib/libdriver_uart.a
bin/STM32F4_DevKit.elf: lib/libdriver_usb.a
bin/STM32F4_DevKit.elf: lib/libstm32f4xx_hal.a
bin/STM32F4_DevKit.elf: lib/libcmsis.a
bin/STM32F4_DevKit.elf: CMakeFiles/STM32F4_DevKit.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking C executable bin\STM32F4_DevKit.elf"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\STM32F4_DevKit.dir\link.txt --verbose=$(VERBOSE)
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold "Generating binary and hex files, showing memory usage"
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-objcopy.exe -O binary C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/bin/STM32F4_DevKit.elf STM32F4_DevKit.bin
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-objcopy.exe -O ihex C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/bin/STM32F4_DevKit.elf STM32F4_DevKit.hex
	C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-size.exe C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/bin/STM32F4_DevKit.elf

# Rule to build all files generated by this target.
CMakeFiles/STM32F4_DevKit.dir/build: bin/STM32F4_DevKit.elf
.PHONY : CMakeFiles/STM32F4_DevKit.dir/build

CMakeFiles/STM32F4_DevKit.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\STM32F4_DevKit.dir\cmake_clean.cmake
.PHONY : CMakeFiles/STM32F4_DevKit.dir/clean

CMakeFiles/STM32F4_DevKit.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles\STM32F4_DevKit.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/STM32F4_DevKit.dir/depend

