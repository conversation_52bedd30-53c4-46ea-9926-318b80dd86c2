# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe

# The command to remove a file.
RM = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake-gui.exe -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named STM32F4_DevKit

# Build rule for target.
STM32F4_DevKit: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 STM32F4_DevKit
.PHONY : STM32F4_DevKit

# fast build rule for target.
STM32F4_DevKit/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/build
.PHONY : STM32F4_DevKit/fast

#=============================================================================
# Target rules for targets named flash

# Build rule for target.
flash: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 flash
.PHONY : flash

# fast build rule for target.
flash/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\flash.dir\build.make CMakeFiles/flash.dir/build
.PHONY : flash/fast

#=============================================================================
# Target rules for targets named debug

# Build rule for target.
debug: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 debug
.PHONY : debug

# fast build rule for target.
debug/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug.dir\build.make CMakeFiles/debug.dir/build
.PHONY : debug/fast

#=============================================================================
# Target rules for targets named board_stm32f407

# Build rule for target.
board_stm32f407: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 board_stm32f407
.PHONY : board_stm32f407

# fast build rule for target.
board_stm32f407/fast:
	$(MAKE) $(MAKESILENT) -f Board_STM32F407\CMakeFiles\board_stm32f407.dir\build.make Board_STM32F407/CMakeFiles/board_stm32f407.dir/build
.PHONY : board_stm32f407/fast

#=============================================================================
# Target rules for targets named driver_i2c

# Build rule for target.
driver_i2c: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 driver_i2c
.PHONY : driver_i2c

# fast build rule for target.
driver_i2c/fast:
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\I2C\CMakeFiles\driver_i2c.dir\build.make DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/build
.PHONY : driver_i2c/fast

#=============================================================================
# Target rules for targets named driver_spi

# Build rule for target.
driver_spi: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 driver_spi
.PHONY : driver_spi

# fast build rule for target.
driver_spi/fast:
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\SPI\CMakeFiles\driver_spi.dir\build.make DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/build
.PHONY : driver_spi/fast

#=============================================================================
# Target rules for targets named driver_usb

# Build rule for target.
driver_usb: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 driver_usb
.PHONY : driver_usb

# fast build rule for target.
driver_usb/fast:
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\USB\CMakeFiles\driver_usb.dir\build.make DeviceDrivers/USB/CMakeFiles/driver_usb.dir/build
.PHONY : driver_usb/fast

#=============================================================================
# Target rules for targets named driver_uart

# Build rule for target.
driver_uart: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 driver_uart
.PHONY : driver_uart

# fast build rule for target.
driver_uart/fast:
	$(MAKE) $(MAKESILENT) -f DeviceDrivers\UART\CMakeFiles\driver_uart.dir\build.make DeviceDrivers/UART/CMakeFiles/driver_uart.dir/build
.PHONY : driver_uart/fast

#=============================================================================
# Target rules for targets named interface_i2c

# Build rule for target.
interface_i2c: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 interface_i2c
.PHONY : interface_i2c

# fast build rule for target.
interface_i2c/fast:
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_I2C\CMakeFiles\interface_i2c.dir\build.make DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/build
.PHONY : interface_i2c/fast

#=============================================================================
# Target rules for targets named interface_spi

# Build rule for target.
interface_spi: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 interface_spi
.PHONY : interface_spi

# fast build rule for target.
interface_spi/fast:
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_SPI\CMakeFiles\interface_spi.dir\build.make DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/build
.PHONY : interface_spi/fast

#=============================================================================
# Target rules for targets named interface_usb

# Build rule for target.
interface_usb: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 interface_usb
.PHONY : interface_usb

# fast build rule for target.
interface_usb/fast:
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_USB\CMakeFiles\interface_usb.dir\build.make DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/build
.PHONY : interface_usb/fast

#=============================================================================
# Target rules for targets named interface_uart

# Build rule for target.
interface_uart: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 interface_uart
.PHONY : interface_uart

# fast build rule for target.
interface_uart/fast:
	$(MAKE) $(MAKESILENT) -f DeviceInterfaces\IF_UART\CMakeFiles\interface_uart.dir\build.make DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/build
.PHONY : interface_uart/fast

#=============================================================================
# Target rules for targets named app_src

# Build rule for target.
app_src: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 app_src
.PHONY : app_src

# fast build rule for target.
app_src/fast:
	$(MAKE) $(MAKESILENT) -f Application\CMakeFiles\app_src.dir\build.make Application/CMakeFiles/app_src.dir/build
.PHONY : app_src/fast

Application/Src/app.obj: Application/Src/app.c.obj
.PHONY : Application/Src/app.obj

# target to build an object file
Application/Src/app.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj
.PHONY : Application/Src/app.c.obj

Application/Src/app.i: Application/Src/app.c.i
.PHONY : Application/Src/app.i

# target to preprocess a source file
Application/Src/app.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.i
.PHONY : Application/Src/app.c.i

Application/Src/app.s: Application/Src/app.c.s
.PHONY : Application/Src/app.s

# target to generate assembly for a file
Application/Src/app.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.s
.PHONY : Application/Src/app.c.s

Application/Src/i2c_impl.obj: Application/Src/i2c_impl.c.obj
.PHONY : Application/Src/i2c_impl.obj

# target to build an object file
Application/Src/i2c_impl.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj
.PHONY : Application/Src/i2c_impl.c.obj

Application/Src/i2c_impl.i: Application/Src/i2c_impl.c.i
.PHONY : Application/Src/i2c_impl.i

# target to preprocess a source file
Application/Src/i2c_impl.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.i
.PHONY : Application/Src/i2c_impl.c.i

Application/Src/i2c_impl.s: Application/Src/i2c_impl.c.s
.PHONY : Application/Src/i2c_impl.s

# target to generate assembly for a file
Application/Src/i2c_impl.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.s
.PHONY : Application/Src/i2c_impl.c.s

Application/Src/main.obj: Application/Src/main.c.obj
.PHONY : Application/Src/main.obj

# target to build an object file
Application/Src/main.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj
.PHONY : Application/Src/main.c.obj

Application/Src/main.i: Application/Src/main.c.i
.PHONY : Application/Src/main.i

# target to preprocess a source file
Application/Src/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.i
.PHONY : Application/Src/main.c.i

Application/Src/main.s: Application/Src/main.c.s
.PHONY : Application/Src/main.s

# target to generate assembly for a file
Application/Src/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.s
.PHONY : Application/Src/main.c.s

Application/Src/uart_impl.obj: Application/Src/uart_impl.c.obj
.PHONY : Application/Src/uart_impl.obj

# target to build an object file
Application/Src/uart_impl.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj
.PHONY : Application/Src/uart_impl.c.obj

Application/Src/uart_impl.i: Application/Src/uart_impl.c.i
.PHONY : Application/Src/uart_impl.i

# target to preprocess a source file
Application/Src/uart_impl.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.i
.PHONY : Application/Src/uart_impl.c.i

Application/Src/uart_impl.s: Application/Src/uart_impl.c.s
.PHONY : Application/Src/uart_impl.s

# target to generate assembly for a file
Application/Src/uart_impl.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.s
.PHONY : Application/Src/uart_impl.c.s

Application/Src/usb_impl.obj: Application/Src/usb_impl.c.obj
.PHONY : Application/Src/usb_impl.obj

# target to build an object file
Application/Src/usb_impl.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj
.PHONY : Application/Src/usb_impl.c.obj

Application/Src/usb_impl.i: Application/Src/usb_impl.c.i
.PHONY : Application/Src/usb_impl.i

# target to preprocess a source file
Application/Src/usb_impl.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.i
.PHONY : Application/Src/usb_impl.c.i

Application/Src/usb_impl.s: Application/Src/usb_impl.c.s
.PHONY : Application/Src/usb_impl.s

# target to generate assembly for a file
Application/Src/usb_impl.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.s
.PHONY : Application/Src/usb_impl.c.s

Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.obj: Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj
.PHONY : Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.obj

# target to build an object file
Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\STM32F4_DevKit.dir\build.make CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj
.PHONY : Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... debug
	@echo ... flash
	@echo ... STM32F4_DevKit
	@echo ... app_src
	@echo ... board_stm32f407
	@echo ... driver_i2c
	@echo ... driver_spi
	@echo ... driver_uart
	@echo ... driver_usb
	@echo ... interface_i2c
	@echo ... interface_spi
	@echo ... interface_uart
	@echo ... interface_usb
	@echo ... Application/Src/app.obj
	@echo ... Application/Src/app.i
	@echo ... Application/Src/app.s
	@echo ... Application/Src/i2c_impl.obj
	@echo ... Application/Src/i2c_impl.i
	@echo ... Application/Src/i2c_impl.s
	@echo ... Application/Src/main.obj
	@echo ... Application/Src/main.i
	@echo ... Application/Src/main.s
	@echo ... Application/Src/uart_impl.obj
	@echo ... Application/Src/uart_impl.i
	@echo ... Application/Src/uart_impl.s
	@echo ... Application/Src/usb_impl.obj
	@echo ... Application/Src/usb_impl.i
	@echo ... Application/Src/usb_impl.s
	@echo ... Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

