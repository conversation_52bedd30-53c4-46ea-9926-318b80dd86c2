# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# compile ASM with C:/ST/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc.exe
# compile C with C:/ST/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc.exe
ASM_DEFINES = -DARM_MATH_CM4 -DDEBUG -DSTM32F407xx -DUSE_HAL_DRIVER -D__FPU_PRESENT=1U

ASM_INCLUDES = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Board_STM32F407\Config C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Board_STM32F407\Startup C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceDrivers\I2C\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceDrivers\SPI\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceDrivers\UART\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceDrivers\USB\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceInterfaces\IF_I2C\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceInterfaces\IF_SPI\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceInterfaces\IF_USB\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceInterfaces\IF_UART\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS\Include C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\ThirdParty\FreeRTOS\include C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\ThirdParty\FATFS\src C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\ThirdParty\USB_HOST\Inc C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Inc\Legacy C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS\Device\ST\STM32F4xx\Include

ASM_FLAGS = -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -x assembler-with-cpp -g -Wall -Wextra -g3 -O0

C_DEFINES = -DARM_MATH_CM4 -DDEBUG -DSTM32F407xx -DUSE_HAL_DRIVER -D__FPU_PRESENT=1U

C_INCLUDES = -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Board_STM32F407\Config -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Board_STM32F407\Startup -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Application\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceDrivers\I2C\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceDrivers\SPI\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceDrivers\UART\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceDrivers\USB\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceInterfaces\IF_I2C\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceInterfaces\IF_SPI\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceInterfaces\IF_USB\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\DeviceInterfaces\IF_UART\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS\Include -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\ThirdParty\FreeRTOS\include -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\ThirdParty\FATFS\src -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\ThirdParty\USB_HOST\Inc -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Inc\Legacy -IC:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS\Device\ST\STM32F4xx\Include

C_FLAGS = -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -std=gnu11 -Wall -Wextra -g3 -O0

