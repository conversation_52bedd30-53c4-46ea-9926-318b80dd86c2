# gcc-arm-none-eabi.cmake
# This file must be specified as CMAKE_TOOLCHAIN_FILE before the first project() call

set(CMAKE_SYSTEM_NAME               Generic)
set(CMAKE_SYSTEM_PROCESSOR          arm)

# Prevent CMake from testing the compiler (must be set before compiler detection)
set(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY)

# Try to find ARM GCC toolchain in common locations
set(POSSIBLE_TOOLCHAIN_PATHS
    "C:/ST/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/"
    "C:/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin/"
    "C:/Program Files (x86)/GNU Arm Embedded Toolchain/9 2020-q2-update/bin/"
    "C:/Program Files/GNU Arm Embedded Toolchain/10 2021.10/bin/"
    "C:/Program Files/GNU Arm Embedded Toolchain/9 2020-q2-update/bin/"
    "C:/msys64/mingw64/bin/"
    "C:/msys64/usr/bin/"
)

# Find the toolchain
set(TO<PERSON>CHAIN_PATH "")
foreach(PATH ${POSSIBLE_TOOLCHAIN_PATHS})
    if(EXISTS "${PATH}arm-none-eabi-gcc.exe")
        set(TOOLCHAIN_PATH ${PATH})
        message(STATUS "Found ARM GCC toolchain at: ${TOOLCHAIN_PATH}")
        break()
    endif()
endforeach()

# If not found, check if it's in PATH
if(NOT TOOLCHAIN_PATH)
    find_program(ARM_GCC_EXECUTABLE arm-none-eabi-gcc)
    if(ARM_GCC_EXECUTABLE)
        get_filename_component(TOOLCHAIN_PATH ${ARM_GCC_EXECUTABLE} DIRECTORY)
        set(TOOLCHAIN_PATH "${TOOLCHAIN_PATH}/")
        message(STATUS "Found ARM GCC toolchain in PATH: ${TOOLCHAIN_PATH}")
    endif()
endif()

# Error if toolchain not found
if(NOT TOOLCHAIN_PATH)
    message(FATAL_ERROR
        "ARM GCC toolchain not found!\n"
        "Please install one of the following:\n"
        "1. STM32CubeCLT (recommended)\n"
        "2. GNU Arm Embedded Toolchain\n"
        "3. Add arm-none-eabi-gcc to your PATH\n"
        "Or manually set TOOLCHAIN_PATH in this file.")
endif()

# Set compilers BEFORE any language is enabled
set(CMAKE_C_COMPILER                ${TOOLCHAIN_PATH}arm-none-eabi-gcc.exe)
set(CMAKE_CXX_COMPILER              ${TOOLCHAIN_PATH}arm-none-eabi-g++.exe)
set(CMAKE_ASM_COMPILER              ${TOOLCHAIN_PATH}arm-none-eabi-gcc.exe)

# MCU flags
set(MCU_FLAGS "-mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard")
set(COMMON_FLAGS "-fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall")

# Some default GCC settings
set(TOOLCHAIN_PREFIX                ${TOOLCHAIN_PATH}arm-none-eabi-)
set(FLAGS                           "${MCU_FLAGS} ${COMMON_FLAGS}")
set(CPP_FLAGS                       "${FLAGS} -fno-rtti -fno-exceptions -fno-threadsafe-statics")

# Set compiler flags with FORCE to override any cached values
set(CMAKE_C_FLAGS                   "${FLAGS}" CACHE STRING "C Compiler flags" FORCE)
set(CMAKE_CXX_FLAGS                 "${CPP_FLAGS}" CACHE STRING "C++ Compiler flags" FORCE)
set(CMAKE_ASM_FLAGS                 "${FLAGS} -x assembler-with-cpp" CACHE STRING "ASM Compiler flags" FORCE)

# Linker flags
set(CMAKE_EXE_LINKER_FLAGS         "-Wl,--gc-sections" CACHE STRING "Linker flags" FORCE)
# Additional tools
set(CMAKE_OBJCOPY                   ${TOOLCHAIN_PATH}arm-none-eabi-objcopy.exe CACHE FILEPATH "objcopy tool")
set(CMAKE_SIZE                      ${TOOLCHAIN_PATH}arm-none-eabi-size.exe CACHE FILEPATH "size tool")
set(CMAKE_OBJDUMP                   ${TOOLCHAIN_PATH}arm-none-eabi-objdump.exe CACHE FILEPATH "objdump tool")

# Set executable suffixes
set(CMAKE_EXECUTABLE_SUFFIX_ASM     ".elf")
set(CMAKE_EXECUTABLE_SUFFIX_C       ".elf")
set(CMAKE_EXECUTABLE_SUFFIX_CXX     ".elf")

# Set the target environment
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

# Disable compiler checks that might fail in cross-compilation
set(CMAKE_C_COMPILER_WORKS 1)
set(CMAKE_CXX_COMPILER_WORKS 1)
set(CMAKE_ASM_COMPILER_WORKS 1)

# Set ASM compilation rules
set(CMAKE_ASM_COMPILE_OBJECT "<CMAKE_ASM_COMPILER> <DEFINES> <INCLUDES> <FLAGS> -o <OBJECT> -c <SOURCE>")
set(CMAKE_ASM_CREATE_STATIC_LIBRARY "<CMAKE_AR> qcs <TARGET> <LINK_FLAGS> <OBJECTS>")
set(CMAKE_ASM_CREATE_SHARED_LIBRARY "<CMAKE_C_COMPILER> <CMAKE_SHARED_LIBRARY_ASM_FLAGS> <LANGUAGE_COMPILE_FLAGS> <LINK_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_ASM_FLAGS> <SONAME_FLAG><TARGET_SONAME> -o <TARGET> <OBJECTS> <LINK_LIBRARIES>")
set(CMAKE_ASM_LINK_EXECUTABLE "<CMAKE_C_COMPILER> <FLAGS> <CMAKE_ASM_LINK_FLAGS> <LINK_FLAGS> <OBJECTS> -o <TARGET> <LINK_LIBRARIES>")