#############################################################################################################################
# file:  CMakeLists.txt
# brief: Template "CMakeLists.txt" for building of executables and static libraries.
#
# usage: Edit "VARIABLES"-section to suit project requirements.
#        For debug build:
#          cmake -DCMAKE_TOOLCHAIN_FILE=Scripts/gcc-arm-none-eabi.cmake  -S ./ -B build -G"MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug
#          mingw32-make -C build VERBOSE=1 -j
#        For release build:
#          cmake -DCMAKE_TOOLCHAIN_FILE=Scripts/gcc-arm-none-eabi.cmake  -S ./ -B build -G"MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release
#          mingw32-make -C build VERBOSE=1 -j
#        Or use the build script: build-gcc-arm-none-eabi.bat
#############################################################################################################################
cmake_minimum_required(VERSION 3.20)


###################### CONSTANTS ######################################
set (PROJECT_TYPE_EXECUTABLE          "exe")
set (PROJECT_TYPE_STATIC_LIBRARY      "static-lib")
set (MCPU_CORTEX_M0				      "-mcpu=cortex-m0")
set (MCPU_CORTEX_M0PLUS				  "-mcpu=cortex-m0plus")
set (MCPU_CORTEX_M3				      "-mcpu=cortex-m3")
set (MCPU_CORTEX_M4				      "-mcpu=cortex-m4")
set (MCPU_CORTEX_M7				      "-mcpu=cortex-m7")
set (MCPU_CORTEX_M33				  "-mcpu=cortex-m33")
set (MCPU_CORTEX_M55				  "-mcpu=cortex-m55")
set (MCPU_CORTEX_M85				  "-mcpu=cortex-m85")
set (MFPU_FPV4_SP_D16                 "-mfpu=fpv4-sp-d16")
set (MFPU_FPV5_D16                    "-mfpu=fpv5-d16")
set (RUNTIME_LIBRARY_NANO            "--specs=nano.specs")
set (RUNTIME_LIBRARY_NOSYS           "--specs=nosys.specs")
set (MFLOAT_ABI_SOFTWARE              "-mfloat-abi=soft")
set (MFLOAT_ABI_HARDWARE              "-mfloat-abi=hard")
set (MFLOAT_ABI_MIX                   "-mfloat-abi=softfp")
#######################################################################

###################### VARIABLES ######################################
set (PROJECT_NAME             "STM32F4_DevKit")
set (PROJECT_TYPE             "exe")
set (LINKER_SCRIPT            "${CMAKE_SOURCE_DIR}/Board_STM32F407/Config/STM32F407VGTX_FLASH.ld")
# MCU flags are handled by the toolchain file (Scripts/gcc-arm-none-eabi.cmake)


set (PROJECT_SOURCES
    # Source files
    ${CMAKE_SOURCE_DIR}/Application/Src/main.c
    ${CMAKE_SOURCE_DIR}/Application/Src/app.c
    ${CMAKE_SOURCE_DIR}/Application/Src/i2c_impl.c
    ${CMAKE_SOURCE_DIR}/Application/Src/uart_impl.c
    ${CMAKE_SOURCE_DIR}/Application/Src/usb_impl.c
    ${CMAKE_SOURCE_DIR}/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s
)

set (PROJECT_DEFINES
    # MCU definition
    STM32F407xx
    USE_HAL_DRIVER
    ARM_MATH_CM4
    __FPU_PRESENT=1U
)

set (PROJECT_INCLUDES
    # Include directories
    ${CMAKE_SOURCE_DIR}/Board_STM32F407/Config
    ${CMAKE_SOURCE_DIR}/Board_STM32F407/Startup
    ${CMAKE_SOURCE_DIR}/Application/Inc
    ${CMAKE_SOURCE_DIR}/DeviceDrivers/I2C/Inc
    ${CMAKE_SOURCE_DIR}/DeviceDrivers/SPI/Inc
    ${CMAKE_SOURCE_DIR}/DeviceDrivers/UART/Inc
    ${CMAKE_SOURCE_DIR}/DeviceDrivers/USB/Inc
    ${CMAKE_SOURCE_DIR}/DeviceInterfaces/IF_I2C/Inc
    ${CMAKE_SOURCE_DIR}/DeviceInterfaces/IF_SPI/Inc
    ${CMAKE_SOURCE_DIR}/DeviceInterfaces/IF_USB/Inc
    ${CMAKE_SOURCE_DIR}/DeviceInterfaces/IF_UART/Inc
    ${CMAKE_SOURCE_DIR}/Middleware/CMSIS/Include
    ${CMAKE_SOURCE_DIR}/Middleware/CMSIS/Device/ST/STM32F4xx/Include
    ${CMAKE_SOURCE_DIR}/Middleware/STM32F4xx_HAL_Driver/Inc
    ${CMAKE_SOURCE_DIR}/ThirdParty/FreeRTOS/Source/include
    ${CMAKE_SOURCE_DIR}/ThirdParty/FATFS/src
    ${CMAKE_SOURCE_DIR}/ThirdParty/USB_HOST/Inc
)

################## PROJECT SETUP ######################################
project(${PROJECT_NAME})

set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_C_EXTENSIONS ON)
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS ON)

# Build configuration options
option(ENABLE_DEBUG_SYMBOLS "Enable debug symbols" ON)
option(ENABLE_OPTIMIZATION "Enable compiler optimizations" OFF)
option(ENABLE_WARNINGS "Enable additional compiler warnings" ON)

# Set build-specific flags
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(ENABLE_DEBUG_SYMBOLS ON)
    set(ENABLE_OPTIMIZATION OFF)
elseif(CMAKE_BUILD_TYPE STREQUAL "Release")
    set(ENABLE_DEBUG_SYMBOLS OFF)
    set(ENABLE_OPTIMIZATION ON)
endif()

# Only include toolchain file if not specified from command line
if(NOT CMAKE_TOOLCHAIN_FILE)
    include("${CMAKE_SOURCE_DIR}/gcc-arm-none-eabi.cmake")
endif()
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/")

# Set build output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Add subdirectories first
add_subdirectory(Board_STM32F407)
add_subdirectory(Middleware)
add_subdirectory(DeviceDrivers)
add_subdirectory(DeviceInterfaces)
add_subdirectory(ThirdParty)
add_subdirectory(Application)


# Configure the executable
if (${PROJECT_TYPE} MATCHES ${PROJECT_TYPE_EXECUTABLE})
    add_executable(${PROJECT_NAME} ${PROJECT_SOURCES})
    
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        ${PROJECT_DEFINES}
        $<$<CONFIG:Debug>:DEBUG>
        $<$<CONFIG:Release>:NDEBUG>
    )

    target_include_directories(${PROJECT_NAME} PRIVATE ${PROJECT_INCLUDES})

    # Set target-specific compile options
    target_compile_options(${PROJECT_NAME} PRIVATE
        $<$<BOOL:${ENABLE_WARNINGS}>:-Wall -Wextra>
        $<$<CONFIG:Debug>:-g3 -O0>
        $<$<CONFIG:Release>:-O2 -DNDEBUG>
    )
    
    # Link with all implemented libraries
    target_link_libraries(${PROJECT_NAME} PRIVATE
        board_stm32f407
        cmsis
        stm32f4xx_hal
        app_src
        # Device drivers
        driver_i2c
        driver_spi
        driver_uart
        driver_usb
        # Device interfaces
        interface_i2c
        interface_spi
        interface_uart
        interface_usb
    )
    
    # Set linker script
    target_link_options(${PROJECT_NAME} PRIVATE 
        -T${LINKER_SCRIPT}
        -Wl,-Map=${PROJECT_NAME}.map
        -Wl,--gc-sections
    )
    
    # Post-build commands to generate binary and hex files
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_OBJCOPY} -O binary $<TARGET_FILE:${PROJECT_NAME}> ${PROJECT_NAME}.bin
        COMMAND ${CMAKE_OBJCOPY} -O ihex $<TARGET_FILE:${PROJECT_NAME}> ${PROJECT_NAME}.hex
        COMMAND ${CMAKE_SIZE} $<TARGET_FILE:${PROJECT_NAME}>
        COMMENT "Generating binary and hex files, showing memory usage"
    )

    # Custom target for flashing (requires ST-LINK)
    add_custom_target(flash
        COMMAND st-flash write ${PROJECT_NAME}.bin 0x8000000
        DEPENDS ${PROJECT_NAME}
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        COMMENT "Flashing ${PROJECT_NAME}.bin to STM32F407"
    )

    # Custom target for debugging with GDB
    add_custom_target(debug
        COMMAND arm-none-eabi-gdb -ex "target remote localhost:3333" -ex "load" -ex "monitor reset halt" $<TARGET_FILE:${PROJECT_NAME}>
        DEPENDS ${PROJECT_NAME}
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        COMMENT "Starting GDB debug session"
    )
endif()
