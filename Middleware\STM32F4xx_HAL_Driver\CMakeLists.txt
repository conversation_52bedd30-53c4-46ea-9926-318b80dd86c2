# STM32F4xx HAL Driver CMakeLists.txt

# Create a minimal HAL library
add_library(stm32f4xx_hal STATIC
    Src/stm32f4xx_hal.c
)

target_include_directories(stm32f4xx_hal PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/Inc
    ${CMAKE_SOURCE_DIR}/Board_STM32F407/Config
    ${CMAKE_SOURCE_DIR}/Middleware/CMSIS/Include
    ${CMAKE_SOURCE_DIR}/Middleware/CMSIS/Device/ST/STM32F4xx/Include
)

target_compile_definitions(stm32f4xx_hal PUBLIC
    STM32F407xx
    USE_HAL_DRIVER
)

# Link with CMSIS
target_link_libraries(stm32f4xx_hal PUBLIC
    cmsis
)
