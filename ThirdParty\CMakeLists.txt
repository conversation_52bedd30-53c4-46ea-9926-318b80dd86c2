# CMakeLists.txt for Third Party Libraries

# Check which third-party components exist and add them
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/FreeRTOS/CMakeLists.txt)
    add_subdirectory(FreeRTOS)
endif()

if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/FATFS/CMakeLists.txt)
    add_subdirectory(FATFS)
endif()

if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/USB_HOST/CMakeLists.txt)
    add_subdirectory(USB_HOST)
endif()

if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/RTOS/CMakeLists.txt)
    add_subdirectory(RTOS)
endif()

# Create a dummy third-party library if no components are found
if(NOT TARGET freertos AND NOT TARGET fatfs AND NOT TARGET usb_host)
    add_library(thirdparty_dummy INTERFACE)
    target_include_directories(thirdparty_dummy INTERFACE
        ${CMAKE_CURRENT_SOURCE_DIR}
    )
endif()
