# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe

# The command to remove a file.
RM = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build

# Include any dependencies generated for this target.
include Middleware/CMSIS/CMakeFiles/cmsis.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include Middleware/CMSIS/CMakeFiles/cmsis.dir/compiler_depend.make

# Include the progress variables for this target.
include Middleware/CMSIS/CMakeFiles/cmsis.dir/progress.make

# Include the compile flags for this target's objects.
include Middleware/CMSIS/CMakeFiles/cmsis.dir/flags.make

Middleware/CMSIS/CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.obj: Middleware/CMSIS/CMakeFiles/cmsis.dir/flags.make
Middleware/CMSIS/CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c
Middleware/CMSIS/CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.obj: Middleware/CMSIS/CMakeFiles/cmsis.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object Middleware/CMSIS/CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\CMSIS && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/CMSIS/CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.obj -MF CMakeFiles\cmsis.dir\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c.obj.d -o CMakeFiles\cmsis.dir\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c

Middleware/CMSIS/CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\CMSIS && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c > CMakeFiles\cmsis.dir\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c.i

Middleware/CMSIS/CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\CMSIS && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c -o CMakeFiles\cmsis.dir\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c.s

# Object files for target cmsis
cmsis_OBJECTS = \
"CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.obj"

# External object files for target cmsis
cmsis_EXTERNAL_OBJECTS =

lib/libcmsis.a: Middleware/CMSIS/CMakeFiles/cmsis.dir/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c.obj
lib/libcmsis.a: Middleware/CMSIS/CMakeFiles/cmsis.dir/build.make
lib/libcmsis.a: Middleware/CMSIS/CMakeFiles/cmsis.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C static library ..\..\lib\libcmsis.a"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\CMSIS && $(CMAKE_COMMAND) -P CMakeFiles\cmsis.dir\cmake_clean_target.cmake
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\CMSIS && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\cmsis.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
Middleware/CMSIS/CMakeFiles/cmsis.dir/build: lib/libcmsis.a
.PHONY : Middleware/CMSIS/CMakeFiles/cmsis.dir/build

Middleware/CMSIS/CMakeFiles/cmsis.dir/clean:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\CMSIS && $(CMAKE_COMMAND) -P CMakeFiles\cmsis.dir\cmake_clean.cmake
.PHONY : Middleware/CMSIS/CMakeFiles/cmsis.dir/clean

Middleware/CMSIS/CMakeFiles/cmsis.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\CMSIS C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\CMSIS C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\CMSIS\CMakeFiles\cmsis.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : Middleware/CMSIS/CMakeFiles/cmsis.dir/depend

