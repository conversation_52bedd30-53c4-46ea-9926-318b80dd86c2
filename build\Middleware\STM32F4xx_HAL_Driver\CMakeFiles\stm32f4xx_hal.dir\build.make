# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe

# The command to remove a file.
RM = C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build

# Include any dependencies generated for this target.
include Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.make

# Include the progress variables for this target.
include Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/progress.make

# Include the compile flags for this target's objects.
include Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_adc.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_adc.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_adc.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_adc.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_adc_ex.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_adc_ex.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_adc_ex.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_adc_ex.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_cortex.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_cortex.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_cortex.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_cortex.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_dma.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_dma.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_dma.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_dma.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_flash.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_flash.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_flash.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_flash.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_flash_ex.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_flash_ex.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_flash_ex.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_flash_ex.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_gpio.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_gpio.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_gpio.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_gpio.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_i2c.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_i2c.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_i2c.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_i2c.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_pwr.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_pwr.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_pwr.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_pwr.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_rcc.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_rcc.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_rcc.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_rcc.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_spi.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_spi.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_spi.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_spi.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_tim.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_tim.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_tim.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_tim.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_tim_ex.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_tim_ex.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_tim_ex.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_tim_ex.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_uart.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_uart.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_uart.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_uart.c.s

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/flags.make
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.obj: C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_usart.c
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.obj: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.obj"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.obj -MF CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_usart.c.obj.d -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_usart.c.obj -c C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_usart.c

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.i"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_usart.c > CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_usart.c.i

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.s"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_usart.c -o CMakeFiles\stm32f4xx_hal.dir\Src\stm32f4xx_hal_usart.c.s

# Object files for target stm32f4xx_hal
stm32f4xx_hal_OBJECTS = \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.obj" \
"CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.obj"

# External object files for target stm32f4xx_hal
stm32f4xx_hal_EXTERNAL_OBJECTS =

lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_adc_ex.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_cortex.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_dma.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_flash_ex.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_gpio.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_i2c.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_pwr.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_rcc.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_spi.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_tim_ex.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_uart.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/Src/stm32f4xx_hal_usart.c.obj
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/build.make
lib/libstm32f4xx_hal.a: Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Linking C static library ..\..\lib\libstm32f4xx_hal.a"
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && $(CMAKE_COMMAND) -P CMakeFiles\stm32f4xx_hal.dir\cmake_clean_target.cmake
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\stm32f4xx_hal.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/build: lib/libstm32f4xx_hal.a
.PHONY : Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/build

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/clean:
	cd /d C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver && $(CMAKE_COMMAND) -P CMakeFiles\stm32f4xx_hal.dir\cmake_clean.cmake
.PHONY : Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/clean

Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\Middleware\STM32F4xx_HAL_Driver C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver C:\MyWorkSpaces\ws_stm32\STM32F4_DevKit\build\Middleware\STM32F4xx_HAL_Driver\CMakeFiles\stm32f4xx_hal.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/depend

