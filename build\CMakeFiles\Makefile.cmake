# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Application/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Board_STM32F407/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/I2C/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/SPI/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/UART/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceDrivers/USB/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_I2C/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_SPI/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_UART/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/DeviceInterfaces/IF_USB/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMSIS/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Scripts/gcc-arm-none-eabi.cmake"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/ThirdParty/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/ThirdParty/FATFS/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/ThirdParty/FreeRTOS/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/ThirdParty/RTOS/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/ThirdParty/RTOS/FreeRTOS-LTS/CMakeLists.txt"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/ThirdParty/USB_HOST/CMakeLists.txt"
  "CMakeFiles/3.28.1/CMakeASMCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeSystem.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeASMCompiler.cmake.in"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeASMInformation.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCCompilerABI.c"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCInformation.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineASMCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeMinGWFindMake.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeSystem.cmake.in"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestASMCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-ASM.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake"
  "C:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Platform/Generic.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.28.1/CMakeSystem.cmake"
  "CMakeFiles/3.28.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "CMakeFiles/3.28.1/CMakeASMCompiler.cmake"
  "Board_STM32F407/CMakeFiles/CMakeDirectoryInformation.cmake"
  "Middleware/CMakeFiles/CMakeDirectoryInformation.cmake"
  "Middleware/STM32F4xx_HAL_Driver/CMakeFiles/CMakeDirectoryInformation.cmake"
  "Middleware/CMSIS/CMakeFiles/CMakeDirectoryInformation.cmake"
  "DeviceDrivers/CMakeFiles/CMakeDirectoryInformation.cmake"
  "DeviceDrivers/I2C/CMakeFiles/CMakeDirectoryInformation.cmake"
  "DeviceDrivers/SPI/CMakeFiles/CMakeDirectoryInformation.cmake"
  "DeviceDrivers/USB/CMakeFiles/CMakeDirectoryInformation.cmake"
  "DeviceDrivers/UART/CMakeFiles/CMakeDirectoryInformation.cmake"
  "DeviceInterfaces/CMakeFiles/CMakeDirectoryInformation.cmake"
  "DeviceInterfaces/IF_I2C/CMakeFiles/CMakeDirectoryInformation.cmake"
  "DeviceInterfaces/IF_SPI/CMakeFiles/CMakeDirectoryInformation.cmake"
  "DeviceInterfaces/IF_USB/CMakeFiles/CMakeDirectoryInformation.cmake"
  "DeviceInterfaces/IF_UART/CMakeFiles/CMakeDirectoryInformation.cmake"
  "ThirdParty/CMakeFiles/CMakeDirectoryInformation.cmake"
  "ThirdParty/FreeRTOS/CMakeFiles/CMakeDirectoryInformation.cmake"
  "ThirdParty/FATFS/CMakeFiles/CMakeDirectoryInformation.cmake"
  "ThirdParty/USB_HOST/CMakeFiles/CMakeDirectoryInformation.cmake"
  "ThirdParty/RTOS/CMakeFiles/CMakeDirectoryInformation.cmake"
  "ThirdParty/RTOS/FreeRTOS-LTS/CMakeFiles/CMakeDirectoryInformation.cmake"
  "Application/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/STM32F4_DevKit.dir/DependInfo.cmake"
  "CMakeFiles/flash.dir/DependInfo.cmake"
  "CMakeFiles/debug.dir/DependInfo.cmake"
  "Board_STM32F407/CMakeFiles/board_stm32f407.dir/DependInfo.cmake"
  "Middleware/STM32F4xx_HAL_Driver/CMakeFiles/stm32f4xx_hal.dir/DependInfo.cmake"
  "Middleware/CMSIS/CMakeFiles/cmsis.dir/DependInfo.cmake"
  "DeviceDrivers/I2C/CMakeFiles/driver_i2c.dir/DependInfo.cmake"
  "DeviceDrivers/SPI/CMakeFiles/driver_spi.dir/DependInfo.cmake"
  "DeviceDrivers/USB/CMakeFiles/driver_usb.dir/DependInfo.cmake"
  "DeviceDrivers/UART/CMakeFiles/driver_uart.dir/DependInfo.cmake"
  "DeviceInterfaces/IF_I2C/CMakeFiles/interface_i2c.dir/DependInfo.cmake"
  "DeviceInterfaces/IF_SPI/CMakeFiles/interface_spi.dir/DependInfo.cmake"
  "DeviceInterfaces/IF_USB/CMakeFiles/interface_usb.dir/DependInfo.cmake"
  "DeviceInterfaces/IF_UART/CMakeFiles/interface_uart.dir/DependInfo.cmake"
  "Application/CMakeFiles/app_src.dir/DependInfo.cmake"
  )
