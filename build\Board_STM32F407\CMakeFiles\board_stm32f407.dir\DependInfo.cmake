
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "ASM"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_ASM
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s" "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/build/Board_STM32F407/CMakeFiles/board_stm32f407.dir/__/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj"
  )
set(CMAKE_ASM_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_ASM
  "ARM_MATH_CM4"
  "STM32F407xx"
  "USE_HAL_DRIVER"
  "__FPU_PRESENT=1U"
  )

# The include file search paths:
set(CMAKE_ASM_TARGET_INCLUDE_PATH
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Board_STM32F407/Config"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Board_STM32F407/Startup"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMSIS/Include"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Inc"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/STM32F4xx_HAL_Driver/Inc/Legacy"
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Middleware/CMSIS/Device/ST/STM32F4xx/Include"
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "C:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Board_STM32F407/Src/system.c" "Board_STM32F407/CMakeFiles/board_stm32f407.dir/Src/system.c.obj" "gcc" "Board_STM32F407/CMakeFiles/board_stm32f407.dir/Src/system.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
