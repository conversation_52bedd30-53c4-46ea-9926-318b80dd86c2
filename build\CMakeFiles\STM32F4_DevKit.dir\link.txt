C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -fdata-sections -ffunction-sections --specs=nano.specs --specs=nosys.specs -Wall -g -Wl,--gc-sections -TC:/MyWorkSpaces/ws_stm32/STM32F4_DevKit/Board_STM32F407/Config/STM32F407VGTX_FLASH.ld -Wl,-Map=STM32F4_DevKit.map -Wl,--gc-sections CMakeFiles/STM32F4_DevKit.dir/Application/Src/main.c.obj CMakeFiles/STM32F4_DevKit.dir/Application/Src/app.c.obj CMakeFiles/STM32F4_DevKit.dir/Application/Src/i2c_impl.c.obj CMakeFiles/STM32F4_DevKit.dir/Application/Src/uart_impl.c.obj CMakeFiles/STM32F4_DevKit.dir/Application/Src/usb_impl.c.obj CMakeFiles/STM32F4_DevKit.dir/Middleware/CMSIS/Device/ST/STM32F4xx/Source/Templates/gcc/startup_stm32f407xx.s.obj Application/CMakeFiles/app_src.dir/Src/app.c.obj Application/CMakeFiles/app_src.dir/Src/main.c.obj Application/CMakeFiles/app_src.dir/Src/i2c_impl.c.obj Application/CMakeFiles/app_src.dir/Src/uart_impl.c.obj Application/CMakeFiles/app_src.dir/Src/usb_impl.c.obj -o bin\STM32F4_DevKit.elf  lib\libboard_stm32f407.a -lcmsis -lstm32f4xx_hal lib\libdriver_i2c.a lib\libdriver_spi.a lib\libdriver_uart.a lib\libdriver_usb.a lib\libinterface_i2c.a lib\libinterface_spi.a lib\libinterface_uart.a lib\libinterface_usb.a lib\libdriver_i2c.a lib\libdriver_spi.a lib\libdriver_uart.a lib\libdriver_usb.a -lstm32f4xx_hal 
